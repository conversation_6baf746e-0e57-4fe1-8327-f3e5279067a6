"""
触发管理器模块

实现智能的景观分析触发机制，包括：
- 多种触发条件
- 自适应阈值调整
- 性能优化的触发策略
- 触发历史跟踪
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from collections import deque

from ..models.data_structures import TriggerCondition, TriggerType
from ..utils.incremental_stats import MovingStatistics


class TriggerManager:
    """
    触发管理器
    
    管理各种触发条件，决定何时执行景观分析。
    """
    
    def __init__(self):
        """初始化触发管理器"""
        # 默认触发条件
        self.trigger_conditions = {
            TriggerType.PERFORMANCE_STAGNATION: TriggerCondition(
                trigger_type=TriggerType.PERFORMANCE_STAGNATION,
                threshold=0.01,  # 改善率阈值
                window_size=10,
                min_interval=5
            ),
            TriggerType.DIVERSITY_DROP: TriggerCondition(
                trigger_type=TriggerType.DIVERSITY_DROP,
                threshold=0.1,   # 多样性下降率阈值
                window_size=5,
                min_interval=3
            ),
            TriggerType.GENERATION_INTERVAL: TriggerCondition(
                trigger_type=TriggerType.GENERATION_INTERVAL,
                threshold=10,    # 代数间隔
                window_size=1,
                min_interval=10
            ),
            TriggerType.STRATEGY_FAILURE: TriggerCondition(
                trigger_type=TriggerType.STRATEGY_FAILURE,
                threshold=0.05,  # 策略失效阈值
                window_size=15,
                min_interval=5
            )
        }
        
        # 历史数据
        self.fitness_history = deque(maxlen=100)
        self.diversity_history = deque(maxlen=50)
        self.trigger_history = []
        
        # 统计信息
        self.performance_stats = MovingStatistics(window_size=20)
        self.diversity_stats = MovingStatistics(window_size=15)
        
        # 自适应参数
        self.adaptive_enabled = True
        self.adaptation_rate = 0.1
        self.last_adaptation_time = time.time()
        
        # 状态信息
        self.last_trigger_info = {}
        self.total_checks = 0
        self.total_triggers = 0
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("触发管理器初始化完成")
    
    def check_triggers(self, generation: int, current_fitness: float,
                      fitness_history: List[float], diversity: float) -> Dict[str, Any]:
        """
        检查所有触发条件
        
        Args:
            generation: 当前代数
            current_fitness: 当前最佳适应度
            fitness_history: 适应度历史
            diversity: 当前多样性
            
        Returns:
            触发信息字典
        """
        self.total_checks += 1
        
        try:
            # 更新历史数据
            self._update_history(current_fitness, diversity)
            
            # 检查各种触发条件
            trigger_results = {}
            should_trigger = False
            triggered_conditions = []
            
            # 1. 性能停滞检查
            stagnation_result = self._check_performance_stagnation(
                generation, fitness_history
            )
            trigger_results["performance_stagnation"] = stagnation_result
            if stagnation_result["triggered"]:
                should_trigger = True
                triggered_conditions.append("performance_stagnation")
            
            # 2. 多样性下降检查
            diversity_result = self._check_diversity_drop(generation, diversity)
            trigger_results["diversity_drop"] = diversity_result
            if diversity_result["triggered"]:
                should_trigger = True
                triggered_conditions.append("diversity_drop")
            
            # 3. 代数间隔检查
            interval_result = self._check_generation_interval(generation)
            trigger_results["generation_interval"] = interval_result
            if interval_result["triggered"]:
                should_trigger = True
                triggered_conditions.append("generation_interval")
            
            # 4. 策略失效检查
            strategy_result = self._check_strategy_failure(
                generation, fitness_history
            )
            trigger_results["strategy_failure"] = strategy_result
            if strategy_result["triggered"]:
                should_trigger = True
                triggered_conditions.append("strategy_failure")
            
            # 创建触发信息
            trigger_info = {
                "should_trigger": should_trigger,
                "triggered_conditions": triggered_conditions,
                "generation": generation,
                "trigger_details": trigger_results,
                "timestamp": time.time()
            }
            
            # 更新触发历史
            if should_trigger:
                self.total_triggers += 1
                self._record_trigger(trigger_info)
                self.logger.info(f"触发景观分析 - 代数: {generation}, 条件: {triggered_conditions}")
            
            # 自适应调整
            if self.adaptive_enabled:
                self._adaptive_adjustment(trigger_info)
            
            self.last_trigger_info = trigger_info
            return trigger_info
            
        except Exception as e:
            self.logger.error(f"检查触发条件时出错: {e}")
            return {
                "should_trigger": False,
                "triggered_conditions": [],
                "generation": generation,
                "error": str(e)
            }
    
    def _check_performance_stagnation(self, generation: int, 
                                    fitness_history: List[float]) -> Dict[str, Any]:
        """检查性能停滞"""
        condition = self.trigger_conditions[TriggerType.PERFORMANCE_STAGNATION]
        
        if not condition.enabled:
            return {"triggered": False, "reason": "disabled"}
        
        # 检查最小间隔
        if generation - condition.last_triggered_generation < condition.min_interval:
            return {"triggered": False, "reason": "min_interval"}
        
        # 需要足够的历史数据
        if len(fitness_history) < condition.window_size:
            return {"triggered": False, "reason": "insufficient_data"}
        
        try:
            # 计算最近窗口的改善率
            recent_fitnesses = fitness_history[-condition.window_size:]
            
            if len(recent_fitnesses) < 2:
                return {"triggered": False, "reason": "insufficient_data"}
            
            # 计算线性趋势（改善率）
            x = np.arange(len(recent_fitnesses))
            slope, _ = np.polyfit(x, recent_fitnesses, 1)
            
            # 更新性能统计
            self.performance_stats.update(slope)
            
            # 判断是否停滞
            is_stagnant = abs(slope) < condition.threshold
            
            result = {
                "triggered": is_stagnant,
                "improvement_rate": float(slope),
                "threshold": condition.threshold,
                "window_size": condition.window_size
            }
            
            if is_stagnant:
                condition.mark_triggered(generation)
                result["reason"] = "performance_stagnation"
            
            return result
            
        except Exception as e:
            self.logger.warning(f"性能停滞检查出错: {e}")
            return {"triggered": False, "reason": "error", "error": str(e)}
    
    def _check_diversity_drop(self, generation: int, diversity: float) -> Dict[str, Any]:
        """检查多样性下降"""
        condition = self.trigger_conditions[TriggerType.DIVERSITY_DROP]
        
        if not condition.enabled:
            return {"triggered": False, "reason": "disabled"}
        
        # 检查最小间隔
        if generation - condition.last_triggered_generation < condition.min_interval:
            return {"triggered": False, "reason": "min_interval"}
        
        try:
            # 更新多样性统计
            self.diversity_stats.update(diversity)
            
            # 需要足够的历史数据
            if len(self.diversity_history) < condition.window_size:
                return {"triggered": False, "reason": "insufficient_data"}
            
            # 计算多样性下降率
            recent_diversities = list(self.diversity_history)[-condition.window_size:]
            
            if len(recent_diversities) < 2:
                return {"triggered": False, "reason": "insufficient_data"}
            
            # 计算下降率
            initial_diversity = recent_diversities[0]
            current_diversity = recent_diversities[-1]
            
            if initial_diversity > 0:
                drop_rate = (initial_diversity - current_diversity) / initial_diversity
            else:
                drop_rate = 0.0
            
            # 判断是否下降过快
            is_dropping = drop_rate > condition.threshold
            
            result = {
                "triggered": is_dropping,
                "drop_rate": float(drop_rate),
                "threshold": condition.threshold,
                "current_diversity": diversity,
                "initial_diversity": initial_diversity
            }
            
            if is_dropping:
                condition.mark_triggered(generation)
                result["reason"] = "diversity_drop"
            
            return result
            
        except Exception as e:
            self.logger.warning(f"多样性下降检查出错: {e}")
            return {"triggered": False, "reason": "error", "error": str(e)}
    
    def _check_generation_interval(self, generation: int) -> Dict[str, Any]:
        """检查代数间隔"""
        condition = self.trigger_conditions[TriggerType.GENERATION_INTERVAL]
        
        if not condition.enabled:
            return {"triggered": False, "reason": "disabled"}
        
        # 计算距离上次触发的间隔
        interval = generation - condition.last_triggered_generation
        
        # 判断是否达到间隔阈值
        is_interval_reached = interval >= condition.threshold
        
        result = {
            "triggered": is_interval_reached,
            "current_interval": interval,
            "threshold": int(condition.threshold),
            "last_triggered": condition.last_triggered_generation
        }
        
        if is_interval_reached:
            condition.mark_triggered(generation)
            result["reason"] = "generation_interval"
        
        return result
    
    def _check_strategy_failure(self, generation: int, 
                              fitness_history: List[float]) -> Dict[str, Any]:
        """检查策略失效"""
        condition = self.trigger_conditions[TriggerType.STRATEGY_FAILURE]
        
        if not condition.enabled:
            return {"triggered": False, "reason": "disabled"}
        
        # 检查最小间隔
        if generation - condition.last_triggered_generation < condition.min_interval:
            return {"triggered": False, "reason": "min_interval"}
        
        try:
            # 需要足够的历史数据
            if len(fitness_history) < condition.window_size:
                return {"triggered": False, "reason": "insufficient_data"}
            
            # 分析最近的性能表现
            recent_fitnesses = fitness_history[-condition.window_size:]
            
            # 计算性能方差（策略一致性指标）
            performance_variance = np.var(recent_fitnesses)
            
            # 计算改善的一致性
            improvements = []
            for i in range(1, len(recent_fitnesses)):
                improvement = recent_fitnesses[i] - recent_fitnesses[i-1]
                improvements.append(improvement)
            
            if len(improvements) == 0:
                return {"triggered": False, "reason": "insufficient_data"}
            
            # 计算改善的标准差（不一致性指标）
            improvement_std = np.std(improvements)
            mean_improvement = np.mean(improvements)
            
            # 策略失效判断：改善不一致且平均改善很小
            is_strategy_failing = (
                improvement_std > condition.threshold and 
                abs(mean_improvement) < condition.threshold
            )
            
            result = {
                "triggered": is_strategy_failing,
                "improvement_std": float(improvement_std),
                "mean_improvement": float(mean_improvement),
                "threshold": condition.threshold,
                "performance_variance": float(performance_variance)
            }
            
            if is_strategy_failing:
                condition.mark_triggered(generation)
                result["reason"] = "strategy_failure"
            
            return result
            
        except Exception as e:
            self.logger.warning(f"策略失效检查出错: {e}")
            return {"triggered": False, "reason": "error", "error": str(e)}
    
    def _update_history(self, fitness: float, diversity: float) -> None:
        """更新历史数据"""
        self.fitness_history.append(fitness)
        self.diversity_history.append(diversity)
    
    def _record_trigger(self, trigger_info: Dict[str, Any]) -> None:
        """记录触发事件"""
        self.trigger_history.append(trigger_info)
        
        # 限制历史大小
        if len(self.trigger_history) > 100:
            self.trigger_history.pop(0)
    
    def _adaptive_adjustment(self, trigger_info: Dict[str, Any]) -> None:
        """自适应调整触发阈值"""
        current_time = time.time()
        
        # 每隔一定时间进行调整
        if current_time - self.last_adaptation_time < 60:  # 60秒间隔
            return
        
        try:
            # 计算触发频率
            trigger_rate = self.total_triggers / max(1, self.total_checks)
            
            # 目标触发频率（可配置）
            target_trigger_rate = 0.1  # 10%
            
            # 调整阈值
            if trigger_rate > target_trigger_rate * 1.5:  # 触发过于频繁
                self._decrease_sensitivity()
            elif trigger_rate < target_trigger_rate * 0.5:  # 触发不够频繁
                self._increase_sensitivity()
            
            self.last_adaptation_time = current_time
            self.logger.debug(f"自适应调整完成，触发率: {trigger_rate:.3f}")
            
        except Exception as e:
            self.logger.warning(f"自适应调整出错: {e}")
    
    def _decrease_sensitivity(self) -> None:
        """降低触发敏感度"""
        for condition in self.trigger_conditions.values():
            if condition.trigger_type == TriggerType.PERFORMANCE_STAGNATION:
                condition.threshold *= (1 - self.adaptation_rate)
            elif condition.trigger_type == TriggerType.DIVERSITY_DROP:
                condition.threshold *= (1 + self.adaptation_rate)
            elif condition.trigger_type == TriggerType.GENERATION_INTERVAL:
                condition.threshold *= (1 + self.adaptation_rate)
    
    def _increase_sensitivity(self) -> None:
        """提高触发敏感度"""
        for condition in self.trigger_conditions.values():
            if condition.trigger_type == TriggerType.PERFORMANCE_STAGNATION:
                condition.threshold *= (1 + self.adaptation_rate)
            elif condition.trigger_type == TriggerType.DIVERSITY_DROP:
                condition.threshold *= (1 - self.adaptation_rate)
            elif condition.trigger_type == TriggerType.GENERATION_INTERVAL:
                condition.threshold *= (1 - self.adaptation_rate)
    
    def get_trigger_statistics(self) -> Dict[str, Any]:
        """获取触发统计信息"""
        trigger_rate = self.total_triggers / max(1, self.total_checks)
        
        # 统计各种触发类型的频率
        trigger_type_counts = {}
        for trigger_info in self.trigger_history:
            for condition_name in trigger_info.get("triggered_conditions", []):
                trigger_type_counts[condition_name] = trigger_type_counts.get(condition_name, 0) + 1
        
        return {
            "total_checks": self.total_checks,
            "total_triggers": self.total_triggers,
            "trigger_rate": trigger_rate,
            "trigger_type_counts": trigger_type_counts,
            "adaptive_enabled": self.adaptive_enabled,
            "current_thresholds": {
                trigger_type.value: condition.threshold 
                for trigger_type, condition in self.trigger_conditions.items()
            }
        }
    
    def get_last_trigger_info(self) -> Dict[str, Any]:
        """获取最后一次触发信息"""
        return self.last_trigger_info.copy()
    
    def configure_trigger(self, trigger_type: TriggerType, 
                         threshold: float = None,
                         window_size: int = None,
                         min_interval: int = None,
                         enabled: bool = None) -> None:
        """
        配置触发条件
        
        Args:
            trigger_type: 触发类型
            threshold: 阈值
            window_size: 窗口大小
            min_interval: 最小间隔
            enabled: 是否启用
        """
        if trigger_type not in self.trigger_conditions:
            self.logger.warning(f"未知的触发类型: {trigger_type}")
            return
        
        condition = self.trigger_conditions[trigger_type]
        
        if threshold is not None:
            condition.threshold = threshold
        if window_size is not None:
            condition.window_size = window_size
        if min_interval is not None:
            condition.min_interval = min_interval
        if enabled is not None:
            condition.enabled = enabled
        
        self.logger.info(f"触发条件已更新: {trigger_type.value}")
    
    def reset(self) -> None:
        """重置触发管理器"""
        # 重置触发条件状态
        for condition in self.trigger_conditions.values():
            condition.last_triggered_generation = -1
            condition.trigger_count = 0
        
        # 清空历史数据
        self.fitness_history.clear()
        self.diversity_history.clear()
        self.trigger_history.clear()
        
        # 重置统计信息
        self.performance_stats = MovingStatistics(window_size=20)
        self.diversity_stats = MovingStatistics(window_size=15)
        
        self.total_checks = 0
        self.total_triggers = 0
        self.last_trigger_info = {}
        
        self.logger.info("触发管理器已重置")
