import numpy as np
from collections import Counter
import logging

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class PopulationAnalyzer:
    @staticmethod
    def analyze_solution_clusters(populations):
        """分析解决方案的聚类情况
        
        Args:
            populations: 种群列表
            
        Returns:
            dict: 聚类分析结果
        """
        if not populations or len(populations) < 2:
            return {'clusters': 0, 'cluster_sizes': []}
            
        # 计算相似度矩阵
        similarity_matrix = []
        for i, p1 in enumerate(populations):
            row = []
            for j, p2 in enumerate(populations):
                if i == j:
                    row.append(1.0)
                else:
                    # 计算路径相似度
                    similarity = PopulationAnalyzer.path_similarity(p1['tour'], p2['tour'])
                    row.append(similarity)
            similarity_matrix.append(row)
        
        # 简单聚类分析 - 基于相似度阈值
        threshold = 0.7
        visited = set()
        clusters = 0
        cluster_sizes = []
        
        for i in range(len(populations)):
            if i in visited:
                continue
                
            # 找到一个新的聚类
            clusters += 1
            cluster_size = 1
            visited.add(i)
            
            # 寻找与当前解相似的其他解
            for j in range(len(populations)):
                if j not in visited and similarity_matrix[i][j] > threshold:
                    visited.add(j)
                    cluster_size += 1
            
            cluster_sizes.append(cluster_size)
        
        return {
            'clusters': clusters,
            'cluster_sizes': cluster_sizes
        }
    
    @staticmethod
    def path_similarity(tour1, tour2):
        """计算两个路径之间的相似度
        
        Args:
            tour1: 第一个路径
            tour2: 第二个路径
            
        Returns:
            float: 相似度值 (0-1)
        """
        # 确保输入是列表
        if isinstance(tour1, np.ndarray):
            tour1 = tour1.tolist()
        if isinstance(tour2, np.ndarray):
            tour2 = tour2.tolist()
            
        # 计算共同边的数量
        edges1 = set()
        for i in range(len(tour1)):
            edge = (tour1[i], tour1[(i+1) % len(tour1)])
            reverse_edge = (tour1[(i+1) % len(tour1)], tour1[i])
            edges1.add(edge)
            edges1.add(reverse_edge)
            
        edges2 = set()
        for i in range(len(tour2)):
            edge = (tour2[i], tour2[(i+1) % len(tour2)])
            reverse_edge = (tour2[(i+1) % len(tour2)], tour2[i])
            edges2.add(edge)
            edges2.add(reverse_edge)
            
        # 计算交集大小并除以2（因为我们计算了正向和反向边）
        common_edges = len(edges1.intersection(edges2)) // 2
        total_edges = len(tour1)
        
        return common_edges / total_edges if total_edges > 0 else 0
    
    @staticmethod
    def common_edge_analysis(populations):
        """分析种群中的共同边
        
        Args:
            populations: 种群列表
            
        Returns:
            float: 共同边比例 (0-1)
        """
        if not populations:
            return 0
            
        # 提取所有路径的边
        edges_list = []
        for ind in populations:
            tour = ind['tour']
            edges = set()
            for i in range(len(tour)):
                edge = (tour[i], tour[(i+1) % len(tour)])
                edges.add(edge)
            edges_list.append(edges)
        
        # 计算共同边
        if not edges_list:
            return 0
            
        common_edges = set.intersection(*edges_list) if edges_list else set()
        return len(common_edges) / len(edges_list[0]) if edges_list and edges_list[0] else 0
    
    @staticmethod
    def analyze_common_features(elite_solutions):
        """分析精英解的共同特征
        
        Args:
            elite_solutions: 精英解列表
            
        Returns:
            dict: 共同特征分析结果
        """
        if not elite_solutions:
            return {}
            
        # 提取所有路径的边
        edge_counter = Counter()
        for solution in elite_solutions:
            tour = solution['tour']
            for i in range(len(tour)):
                edge = (tour[i], tour[(i+1) % len(tour)])
                edge_counter[edge] += 1
        
        # 找出高频边
        total = len(elite_solutions)
        common_edges = {str(edge): count/total for edge, count in edge_counter.items() 
                       if count/total >= 0.7}  # 70%以上的精英解共有
        
        return {
            'common_edges': common_edges,
            'common_edge_ratio': len(common_edges) / len(elite_solutions[0]['tour']) if elite_solutions else 0
        }
    
    @staticmethod
    def analyze_population_gap(elite_solutions, populations):
        """分析精英解与当前种群的差异
        
        Args:
            elite_solutions: 精英解列表
            populations: 当前种群列表
            
        Returns:
            dict: 差异分析结果
        """
        if not elite_solutions or not populations:
            return {}
            
        # 计算成本差异
        elite_costs = [s['cur_cost'] for s in elite_solutions]
        pop_costs = [p['cur_cost'] for p in populations]
        
        cost_gap = {
            'min_gap': min(pop_costs) - min(elite_costs) if elite_costs and pop_costs else 0,
            'avg_gap': (sum(pop_costs) / len(pop_costs)) - (sum(elite_costs) / len(elite_costs)) 
                      if elite_costs and pop_costs else 0
        }
        
        # 计算结构差异
        elite_edges = set()
        for solution in elite_solutions:
            tour = solution['tour']
            for i in range(len(tour)):
                edge = (tour[i], tour[(i+1) % len(tour)])
                elite_edges.add(edge)
        
        pop_edges = set()
        for ind in populations:
            tour = ind['tour']
            for i in range(len(tour)):
                edge = (tour[i], tour[(i+1) % len(tour)])
                pop_edges.add(edge)
        
        structure_gap = {
            'unique_elite_edges': len(elite_edges - pop_edges),
            'unique_pop_edges': len(pop_edges - elite_edges),
            'common_edges': len(elite_edges & pop_edges)
        }
        
        return {
            'cost_gap': cost_gap,
            'structure_gap': structure_gap
        }
    
    @staticmethod
    def identify_fixed_nodes(elite_solutions):
        """识别精英解中的固定位置节点
        
        Args:
            elite_solutions: 精英解列表
            
        Returns:
            list: 固定位置节点列表
        """
        if not elite_solutions or len(elite_solutions) < 2:
            return []
            
        # 提取所有路径
        tours = [solution['tour'] for solution in elite_solutions]
        
        # 找出在所有精英解中位置相同的节点
        fixed_nodes = []
        tour_length = len(tours[0])
        
        for pos in range(tour_length):
            node_at_pos = tours[0][pos]
            is_fixed = True
            
            for tour in tours[1:]:
                if tour[pos] != node_at_pos:
                    is_fixed = False
                    break
            
            if is_fixed:
                fixed_nodes.append({'node': node_at_pos, 'position': pos})
        
        return fixed_nodes
    
    @staticmethod
    def analyze_elite_diversity(elite_solutions):
        """分析精英解的多样性
        
        Args:
            elite_solutions: 精英解列表
            
        Returns:
            dict: 多样性分析结果
        """
        if not elite_solutions or len(elite_solutions) < 2:
            return {'diversity_score': 0}
            
        # 计算两两之间的相似度
        similarities = []
        for i in range(len(elite_solutions)):
            for j in range(i+1, len(elite_solutions)):
                similarity = PopulationAnalyzer.path_similarity(
                    elite_solutions[i]['tour'], 
                    elite_solutions[j]['tour']
                )
                similarities.append(similarity)
        
        # 计算多样性分数 (1 - 平均相似度)
        avg_similarity = sum(similarities) / len(similarities) if similarities else 0
        diversity_score = 1 - avg_similarity
        
        return {'diversity_score': diversity_score}