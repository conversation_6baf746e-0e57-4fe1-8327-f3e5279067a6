# 景观指导进化算法系统实现方案

## 1. 系统架构设计

### 1.1 整体架构

```mermaid
graph TB
    subgraph "景观指导进化算法系统"
        subgraph "景观分析层"
            LA[LandscapeAnalyzer]
            FC[FeatureCalculator]
            HC[HistoryCache]
            TM[TriggerManager]
        end
        
        subgraph "LLM策略决策层"
            SP[StrategyPromptGenerator]
            LI[LLMInterface]
            SE[StrategyExecutor]
            PC[ParameterController]
        end
        
        subgraph "精英解管理层"
            EA[EliteArchive]
            DM[DiversityManager]
            QA[QualityAssessor]
        end
        
        subgraph "可视化层"
            VE[VisualizationEngine]
            WR[WebRenderer]
            UI[UserInterface]
        end
        
        subgraph "现有系统集成"
            EM[ExpertManager]
            GA[GeneticAlgorithm]
            TSP[TSPSolver]
        end
    end
    
    LA --> FC
    FC --> HC
    HC --> TM
    TM --> SP
    SP --> LI
    LI --> SE
    SE --> PC
    PC --> GA
    
    GA --> EA
    EA --> DM
    DM --> QA
    QA --> LA
    
    LA --> VE
    VE --> WR
    WR --> UI
    
    EM --> LA
    GA --> TSP
```

### 1.2 核心组件接口定义

#### 1.2.1 景观分析接口

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np

@dataclass
class LandscapeFeatures:
    """景观特征数据结构"""
    ruggedness: float
    modality: float
    convergence: float
    information_content: float
    correlation_length: float
    local_optima_density: float
    confidence: float
    timestamp: float

@dataclass
class PopulationState:
    """种群状态数据结构"""
    individuals: List[Any]
    fitnesses: np.ndarray
    diversity: float
    best_fitness: float
    worst_fitness: float
    mean_fitness: float
    std_fitness: float
    generation: int

class ILandscapeAnalyzer(ABC):
    """景观分析器接口"""
    
    @abstractmethod
    def analyze(self, population_state: PopulationState) -> LandscapeFeatures:
        """分析当前种群的景观特征"""
        pass
    
    @abstractmethod
    def should_trigger_analysis(self, generation: int, performance_history: List[float]) -> bool:
        """判断是否应该触发景观分析"""
        pass
    
    @abstractmethod
    def update_incremental(self, new_individuals: List[Any], new_fitnesses: np.ndarray) -> None:
        """增量更新景观特征"""
        pass
```

#### 1.2.2 LLM策略决策接口

```python
@dataclass
class StrategyDecision:
    """策略决策结果"""
    global_strategy: str  # 'exploration', 'exploitation', 'transition'
    individual_strategies: Dict[int, Dict[str, float]]  # 个体级策略参数
    confidence: float
    reasoning: str
    parameters: Dict[str, float]

class IStrategySelector(ABC):
    """策略选择器接口"""
    
    @abstractmethod
    def select_strategy(self, 
                       landscape_features: LandscapeFeatures,
                       population_state: PopulationState,
                       history: Optional[List[Dict]] = None) -> StrategyDecision:
        """基于景观特征选择策略"""
        pass
    
    @abstractmethod
    def format_prompt(self, 
                     landscape_features: LandscapeFeatures,
                     population_state: PopulationState) -> str:
        """格式化LLM提示词"""
        pass
```

#### 1.2.3 精英解存档接口

```python
@dataclass
class EliteSolution:
    """精英解数据结构"""
    solution: Any
    fitness: float
    diversity_score: float
    age: int
    discovery_generation: int
    neighborhood_quality: float

class IEliteArchive(ABC):
    """精英解存档接口"""
    
    @abstractmethod
    def add_solution(self, solution: Any, fitness: float, generation: int) -> bool:
        """添加解到存档"""
        pass
    
    @abstractmethod
    def get_diverse_solutions(self, k: int) -> List[EliteSolution]:
        """获取多样化的精英解"""
        pass
    
    @abstractmethod
    def update_archive(self, generation: int) -> None:
        """更新存档（清理过时解等）"""
        pass
```

## 2. 核心算法实现

### 2.1 景观特征计算算法

#### 2.1.1 崎岖度计算

```python
import numpy as np
from scipy.stats import pearsonr
from typing import List, Tuple
import logging

class RuggednessCalculator:
    """崎岖度计算器"""
    
    def __init__(self, window_size: int = 50):
        self.window_size = window_size
        self.fitness_history = []
        self.logger = logging.getLogger(__name__)
    
    def calculate_autocorrelation(self, fitnesses: np.ndarray, distance: int = 1) -> float:
        """
        计算自相关函数
        ρ(d) = Cov(f(s), f(s+d)) / Var(f(s))
        """
        if len(fitnesses) <= distance:
            return 0.0
        
        # 计算滞后序列
        f_t = fitnesses[:-distance]
        f_t_d = fitnesses[distance:]
        
        if len(f_t) == 0 or len(f_t_d) == 0:
            return 0.0
        
        # 计算皮尔逊相关系数
        try:
            correlation, _ = pearsonr(f_t, f_t_d)
            return correlation if not np.isnan(correlation) else 0.0
        except Exception as e:
            self.logger.warning(f"计算自相关函数时出错: {e}")
            return 0.0
    
    def calculate_correlation_length(self, autocorr_1: float) -> float:
        """
        计算相关长度
        τ = -1 / ln|ρ(1)|
        """
        if abs(autocorr_1) < 1e-10:  # 避免log(0)
            return float('inf')
        
        try:
            tau = -1.0 / np.log(abs(autocorr_1))
            return tau if tau > 0 else float('inf')
        except (ValueError, ZeroDivisionError):
            return float('inf')
    
    def calculate_ruggedness(self, fitnesses: np.ndarray) -> Tuple[float, float]:
        """
        计算景观崎岖度
        返回: (ruggedness, correlation_length)
        """
        if len(fitnesses) < 2:
            return 0.5, float('inf')
        
        # 计算距离为1的自相关系数
        autocorr_1 = self.calculate_autocorrelation(fitnesses, distance=1)
        
        # 计算崎岖度 (1 - |ρ(1)|)
        ruggedness = 1.0 - abs(autocorr_1)
        
        # 计算相关长度
        correlation_length = self.calculate_correlation_length(autocorr_1)
        
        return ruggedness, correlation_length
```

#### 2.1.2 多模态性分析

```python
from sklearn.cluster import DBSCAN
from scipy.spatial.distance import pdist, squareform
import numpy as np

class ModalityAnalyzer:
    """多模态性分析器"""
    
    def __init__(self, eps: float = 0.1, min_samples: int = 3):
        self.eps = eps
        self.min_samples = min_samples
        self.logger = logging.getLogger(__name__)
    
    def detect_local_optima(self, solutions: List[Any], fitnesses: np.ndarray, 
                           distance_func) -> List[int]:
        """检测局部最优解"""
        local_optima = []
        
        for i, (solution, fitness) in enumerate(zip(solutions, fitnesses)):
            is_local_optimum = True
            
            # 检查邻域内是否有更好的解
            for j, (other_solution, other_fitness) in enumerate(zip(solutions, fitnesses)):
                if i != j:
                    distance = distance_func(solution, other_solution)
                    if distance <= self.eps and other_fitness > fitness:
                        is_local_optimum = False
                        break
            
            if is_local_optimum:
                local_optima.append(i)
        
        return local_optima
    
    def calculate_modality(self, solutions: List[Any], fitnesses: np.ndarray,
                          distance_func) -> Tuple[float, int, List[int]]:
        """
        计算多模态性
        返回: (modality_score, num_local_optima, local_optima_indices)
        """
        if len(solutions) < self.min_samples:
            return 0.0, 0, []
        
        # 检测局部最优解
        local_optima = self.detect_local_optima(solutions, fitnesses, distance_func)
        
        # 计算多模态性分数
        modality_score = len(local_optima) / len(solutions)
        
        return modality_score, len(local_optima), local_optima
    
    def analyze_solution_clusters(self, solutions: List[Any], 
                                 distance_matrix: np.ndarray) -> Dict[str, Any]:
        """分析解的聚类结构"""
        if len(solutions) < self.min_samples:
            return {"num_clusters": 0, "cluster_labels": [], "cluster_sizes": []}
        
        # 使用DBSCAN进行聚类
        clustering = DBSCAN(eps=self.eps, min_samples=self.min_samples, 
                           metric='precomputed')
        cluster_labels = clustering.fit_predict(distance_matrix)
        
        # 统计聚类信息
        unique_labels = set(cluster_labels)
        num_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        
        cluster_sizes = []
        for label in unique_labels:
            if label != -1:  # 排除噪声点
                cluster_size = np.sum(cluster_labels == label)
                cluster_sizes.append(cluster_size)
        
        return {
            "num_clusters": num_clusters,
            "cluster_labels": cluster_labels.tolist(),
            "cluster_sizes": cluster_sizes,
            "noise_points": np.sum(cluster_labels == -1)
        }

#### 2.1.3 收敛状态评估

```python
class ConvergenceAnalyzer:
    """收敛状态分析器"""

    def __init__(self, window_size: int = 20):
        self.window_size = window_size
        self.fitness_variance_history = []
        self.initial_variance = None
        self.logger = logging.getLogger(__name__)

    def calculate_convergence(self, fitnesses: np.ndarray, generation: int) -> float:
        """
        计算收敛状态
        Convergence(t) = 1 - (σ_fitness(t) / σ_fitness(0))
        """
        current_variance = np.var(fitnesses)

        # 记录初始方差
        if self.initial_variance is None:
            self.initial_variance = current_variance
            return 0.0

        # 避免除零
        if self.initial_variance < 1e-10:
            return 1.0 if current_variance < 1e-10 else 0.0

        # 计算收敛度
        convergence = 1.0 - (current_variance / self.initial_variance)
        convergence = max(0.0, min(1.0, convergence))  # 限制在[0,1]范围内

        # 更新历史记录
        self.fitness_variance_history.append(current_variance)
        if len(self.fitness_variance_history) > self.window_size:
            self.fitness_variance_history.pop(0)

        return convergence

    def calculate_improvement_rate(self, fitness_history: List[float],
                                  window_size: int = 10) -> float:
        """计算改善率"""
        if len(fitness_history) < window_size:
            return 0.0

        recent_fitnesses = fitness_history[-window_size:]
        if len(recent_fitnesses) < 2:
            return 0.0

        # 计算线性趋势
        x = np.arange(len(recent_fitnesses))
        slope, _ = np.polyfit(x, recent_fitnesses, 1)

        return slope

#### 2.1.4 信息内容计算

```python
from collections import Counter
import math

class InformationContentCalculator:
    """信息内容计算器"""

    def __init__(self, precision: float = 0.01):
        self.precision = precision
        self.logger = logging.getLogger(__name__)

    def discretize_fitnesses(self, fitnesses: np.ndarray) -> List[str]:
        """将适应度值离散化为符号序列"""
        if len(fitnesses) < 2:
            return ['0']

        # 计算相邻适应度值的差异
        differences = np.diff(fitnesses)

        # 根据差异的符号和大小生成符号
        symbols = []
        for diff in differences:
            if abs(diff) < self.precision:
                symbols.append('=')  # 相等
            elif diff > 0:
                symbols.append('+')  # 增加
            else:
                symbols.append('-')  # 减少

        return symbols

    def calculate_entropy(self, symbols: List[str], block_size: int = 1) -> float:
        """计算符号序列的熵"""
        if len(symbols) < block_size:
            return 0.0

        # 生成块序列
        blocks = []
        for i in range(len(symbols) - block_size + 1):
            block = ''.join(symbols[i:i + block_size])
            blocks.append(block)

        # 计算块的频率
        block_counts = Counter(blocks)
        total_blocks = len(blocks)

        # 计算熵
        entropy = 0.0
        for count in block_counts.values():
            probability = count / total_blocks
            if probability > 0:
                entropy -= probability * math.log2(probability)

        return entropy

    def calculate_information_content(self, fitnesses: np.ndarray) -> float:
        """计算信息内容"""
        symbols = self.discretize_fitnesses(fitnesses)

        if len(symbols) == 0:
            return 0.0

        # 计算不同块大小的熵
        max_block_size = min(6, len(symbols))
        entropies = []

        for block_size in range(1, max_block_size + 1):
            entropy = self.calculate_entropy(symbols, block_size)
            entropies.append(entropy)

        # 返回平均熵作为信息内容
        return np.mean(entropies) if entropies else 0.0

### 2.2 增量更新算法

#### 2.2.1 滑动窗口管理

```python
from collections import deque
from typing import Deque

class SlidingWindowManager:
    """滑动窗口管理器"""

    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.fitness_window: Deque[float] = deque(maxlen=max_size)
        self.solution_window: Deque[Any] = deque(maxlen=max_size)
        self.generation_window: Deque[int] = deque(maxlen=max_size)
        self.logger = logging.getLogger(__name__)

    def add_samples(self, solutions: List[Any], fitnesses: np.ndarray,
                   generation: int) -> None:
        """添加新样本到滑动窗口"""
        for solution, fitness in zip(solutions, fitnesses):
            self.solution_window.append(solution)
            self.fitness_window.append(fitness)
            self.generation_window.append(generation)

    def get_recent_samples(self, n: int = None) -> Tuple[List[Any], np.ndarray]:
        """获取最近的n个样本"""
        if n is None:
            n = len(self.solution_window)

        n = min(n, len(self.solution_window))

        recent_solutions = list(self.solution_window)[-n:]
        recent_fitnesses = np.array(list(self.fitness_window)[-n:])

        return recent_solutions, recent_fitnesses

    def get_window_statistics(self) -> Dict[str, float]:
        """获取窗口统计信息"""
        if len(self.fitness_window) == 0:
            return {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}

        fitnesses = np.array(self.fitness_window)
        return {
            "mean": np.mean(fitnesses),
            "std": np.std(fitnesses),
            "min": np.min(fitnesses),
            "max": np.max(fitnesses),
            "size": len(fitnesses)
        }

#### 2.2.2 增量统计计算

```python
class IncrementalStatistics:
    """增量统计计算器（Welford算法）"""

    def __init__(self):
        self.count = 0
        self.mean = 0.0
        self.m2 = 0.0  # 平方差的累积
        self.min_value = float('inf')
        self.max_value = float('-inf')

    def update(self, value: float) -> None:
        """更新统计量"""
        self.count += 1
        delta = value - self.mean
        self.mean += delta / self.count
        delta2 = value - self.mean
        self.m2 += delta * delta2

        self.min_value = min(self.min_value, value)
        self.max_value = max(self.max_value, value)

    def get_variance(self) -> float:
        """获取方差"""
        if self.count < 2:
            return 0.0
        return self.m2 / (self.count - 1)

    def get_std(self) -> float:
        """获取标准差"""
        return math.sqrt(self.get_variance())

    def get_statistics(self) -> Dict[str, float]:
        """获取所有统计量"""
        return {
            "count": self.count,
            "mean": self.mean,
            "variance": self.get_variance(),
            "std": self.get_std(),
            "min": self.min_value if self.count > 0 else 0.0,
            "max": self.max_value if self.count > 0 else 0.0
        }
```
