# 统一路径相似度优化模块
# 合并了原有多个相似度计算模块的最佳实现
# 提供高效的路径存储、相似度比较和批量计算机制

import numpy as np
import time
from collections import defaultdict
import logging
from numba import jit, prange

# 获取日志记录器
logger = logging.getLogger(__name__)


@jit(nopython=True)
def calculate_edge_similarity_jit(path1, path2):
    """使用JIT优化的边相似度计算函数

    参数:
        path1, path2: numpy数组格式的路径

    返回:
        float: 相似度，范围[0,1]
    """
    if len(path1) != len(path2):
        return 0.0

    path_length = len(path1)
    if path_length == 0:
        return 1.0

    # 构建边集合
    edges1 = set()
    edges2 = set()

    for i in range(path_length):
        # 获取边的两个节点
        node1_a, node1_b = path1[i], path1[(i + 1) % path_length]
        node2_a, node2_b = path2[i], path2[(i + 1) % path_length]

        # 无向边，确保顺序一致
        edge1 = (min(node1_a, node1_b), max(node1_a, node1_b))
        edge2 = (min(node2_a, node2_b), max(node2_a, node2_b))

        edges1.add(edge1)
        edges2.add(edge2)

    # 计算共享边数量
    shared_edges = 0
    for edge in edges1:
        if edge in edges2:
            shared_edges += 1

    return shared_edges / path_length


@jit(nopython=True, parallel=True)
def batch_calculate_similarity_jit(path, paths):
    """批量计算一个路径与多个路径的相似度（JIT优化）

    参数:
        path: 待比较的路径
        paths: 路径列表

    返回:
        similarities: 相似度列表
    """
    n_paths = len(paths)
    similarities = np.zeros(n_paths)

    for i in prange(n_paths):
        similarities[i] = calculate_edge_similarity_jit(path, paths[i])

    return similarities


class PathSimilarityOptimizer:
    """统一的路径相似度优化器，合并了多个实现的最佳特性"""

    def __init__(self, similarity_threshold=0.9, max_cache_size=1000, use_jit=True):
        """初始化路径相似度优化器

        参数:
            similarity_threshold: 相似度阈值，当路径相似度超过此阈值时，认为路径相似
            max_cache_size: 最大缓存大小
            use_jit: 是否使用JIT优化计算
        """
        self.similarity_threshold = similarity_threshold
        self.max_cache_size = max_cache_size
        self.use_jit = use_jit

        # 路径存储
        self.paths = []
        self.path_features = {}
        self.path_length_index = defaultdict(list)

        # 统计信息
        self.path_count = 0
        self.cache_hits = 0
        self.similarity_calculations = 0

        # 缓存机制
        self.similarity_cache = {}

        # 批量计算阈值
        self.batch_threshold = 20

    def _extract_path_features(self, path):
        """提取路径特征，生成路径特征向量或指纹

        参数:
            path: 路径

        返回:
            dict: 路径特征字典，包含边集合、节点频率等特征
        """
        # 标准化路径格式
        if isinstance(path, np.ndarray):
            path = path.tolist()

        # 提取边集合（无向边）
        edges = set()
        for i in range(len(path)):
            node1 = path[i]
            node2 = path[(i + 1) % len(path)]
            edge = (min(node1, node2), max(node1, node2))
            edges.add(edge)

        # 生成路径特征字典
        features = {
            'edges': edges,
            'path_length': len(path),
            'edge_count': len(edges)
        }

        return features

    def _calculate_similarity_cached(self, path1, path2):
        """带缓存的相似度计算

        参数:
            path1, path2: 两个路径

        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        self.similarity_calculations += 1

        # 标准化路径格式用于缓存键
        if isinstance(path1, np.ndarray):
            path1_key = tuple(path1.tolist())
        else:
            path1_key = tuple(path1)

        if isinstance(path2, np.ndarray):
            path2_key = tuple(path2.tolist())
        else:
            path2_key = tuple(path2)

        # 生成缓存键（确保顺序一致）
        cache_key = (min(path1_key, path2_key), max(path1_key, path2_key))

        # 检查缓存
        if cache_key in self.similarity_cache:
            self.cache_hits += 1
            return self.similarity_cache[cache_key]

        # 计算相似度
        if self.use_jit:
            # 使用JIT优化计算
            path1_array = np.array(path1, dtype=np.int64) if not isinstance(path1, np.ndarray) else path1.astype(np.int64)
            path2_array = np.array(path2, dtype=np.int64) if not isinstance(path2, np.ndarray) else path2.astype(np.int64)
            similarity = calculate_edge_similarity_jit(path1_array, path2_array)
        else:
            # 使用特征计算
            features1 = self._extract_path_features(path1)
            features2 = self._extract_path_features(path2)
            similarity = self._calculate_similarity_fast(features1, features2)

        # 缓存结果（如果缓存未满）
        if len(self.similarity_cache) < self.max_cache_size:
            self.similarity_cache[cache_key] = similarity

        return similarity

    def _calculate_similarity_fast(self, features1, features2):
        """快速计算两个路径特征之间的相似度

        参数:
            features1, features2: 两个路径特征字典

        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        if features1['path_length'] != features2['path_length']:
            return 0.0

        # 计算共享边数量
        shared_edges = len(features1['edges'].intersection(features2['edges']))

        # 计算相似度：共享边数量除以路径长度
        return shared_edges / features1['path_length']

    def add_path(self, path):
        """添加路径到存储集合

        参数:
            path: 待添加的路径

        返回:
            int: 路径ID
        """
        # 标准化路径格式
        if isinstance(path, np.ndarray):
            path_list = path.tolist()
        else:
            path_list = list(path)

        # 存储路径
        path_id = self.path_count
        self.paths.append(path_list)

        # 按长度索引路径
        path_length = len(path_list)
        self.path_length_index[path_length].append(path_id)

        # 提取并存储特征
        features = self._extract_path_features(path_list)
        self.path_features[path_id] = features

        self.path_count += 1
        return path_id

    def check_similarity(self, path):
        """检查路径与已存储路径的相似度

        参数:
            path: 待检查的路径

        返回:
            tuple: (是否相似, 最相似路径ID, 最大相似度)
        """
        if not self.paths:
            return False, -1, 0.0

        # 标准化路径格式
        if isinstance(path, np.ndarray):
            path_list = path.tolist()
        else:
            path_list = list(path)

        path_length = len(path_list)
        max_similarity = 0.0
        most_similar_id = -1

        # 只检查相同长度的路径
        candidate_ids = self.path_length_index.get(path_length, [])

        if len(candidate_ids) > self.batch_threshold and self.use_jit:
            # 使用批量JIT计算
            candidate_paths = [np.array(self.paths[i], dtype=np.int64) for i in candidate_ids]
            path_array = np.array(path_list, dtype=np.int64)
            similarities = batch_calculate_similarity_jit(path_array, np.array(candidate_paths))

            max_idx = np.argmax(similarities)
            max_similarity = similarities[max_idx]
            most_similar_id = candidate_ids[max_idx]
        else:
            # 逐个计算相似度
            for path_id in candidate_ids:
                stored_path = self.paths[path_id]
                similarity = self._calculate_similarity_cached(path_list, stored_path)

                if similarity > max_similarity:
                    max_similarity = similarity
                    most_similar_id = path_id

        is_similar = max_similarity >= self.similarity_threshold
        return is_similar, most_similar_id, max_similarity

    def calculate_similarity(self, path1, path2):
        """计算两条路径之间的相似度

        参数:
            path1, path2: 两个路径

        返回:
            float: 相似度，范围[0,1]
        """
        return self._calculate_similarity_cached(path1, path2)

    def get_statistics(self):
        """获取优化器统计信息

        返回:
            dict: 统计信息字典
        """
        cache_hit_rate = self.cache_hits / max(self.similarity_calculations, 1)

        return {
            'stored_paths': self.path_count,
            'similarity_calculations': self.similarity_calculations,
            'cache_hits': self.cache_hits,
            'cache_hit_rate': cache_hit_rate,
            'cache_size': len(self.similarity_cache),
            'max_cache_size': self.max_cache_size
        }

    def clear_cache(self):
        """清空相似度计算缓存"""
        self.similarity_cache.clear()
        self.cache_hits = 0
        self.similarity_calculations = 0
    
    def _filter_candidates(self, features):
        """使用路径特征进行初步筛选，减少需要详细比较的路径数量
        
        参数:
            features: 路径特征
            
        返回:
            list: 候选路径ID列表
        """
        # 如果路径数量较少，返回所有路径ID
        if self.path_count < 10:
            return list(self.path_features.keys())
        
        # 使用路径长度进行初步筛选
        candidates = []
        for path_id, path_features in self.path_features.items():
            # 路径长度必须相同
            if path_features['path_length'] != features['path_length']:
                continue
                
            # 边数量应该接近
            if abs(path_features['edge_count'] - features['edge_count']) > 5:
                continue
                
            # 添加到候选列表
            candidates.append(path_id)
        
        return candidates
    
    def calculate_similarity(self, path1, path2):
        """计算两条路径之间的相似度
        
        参数:
            path1, path2: 两个路径
            
        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        return self._calculate_similarity_cached(path1, path2)
    
    def get_statistics(self):
        """获取统计信息
        
        返回:
            dict: 统计信息字典
        """
        return {
            'path_count': self.path_count,
            'cache_hits': self.cache_hits,
            'similarity_calculations': self.similarity_calculations,
            'cache_hit_rate': self.cache_hits / max(1, self.similarity_calculations),
            'cache_size': len(self.similarity_cache)
        }
    
    def clear_cache(self):
        """清除缓存"""
        self.similarity_cache.clear()
        self.cache_hits = 0
        self.similarity_calculations = 0