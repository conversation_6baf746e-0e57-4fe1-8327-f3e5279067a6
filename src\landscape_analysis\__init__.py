"""
景观指导进化算法系统 - 景观分析模块

该模块提供完整的适应度景观分析功能，包括：
- 景观特征计算（崎岖度、多模态性、收敛状态等）
- 增量更新机制
- 历史信息管理
- 智能触发机制

作者: 景观指导EA系统
版本: 1.0.0
"""

from .core.landscape_analyzer import LandscapeAnalyzer
from .core.feature_calculator import FeatureCalculator
from .core.history_cache import HistoryCache
from .core.trigger_manager import TriggerManager
from .models.data_structures import (
    LandscapeFeatures,
    PopulationState,
    AnalysisResult,
    TriggerCondition
)
from .utils.incremental_stats import IncrementalStatistics
from .utils.sliding_window import SlidingWindowManager

__all__ = [
    'LandscapeAnalyzer',
    'FeatureCalculator', 
    'HistoryCache',
    'TriggerManager',
    'LandscapeFeatures',
    'PopulationState',
    'AnalysisResult',
    'TriggerCondition',
    'IncrementalStatistics',
    'SlidingWindowManager'
]

__version__ = "1.0.0"
__author__ = "景观指导EA系统"
