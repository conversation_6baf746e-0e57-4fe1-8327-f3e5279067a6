"""
特征计算器模块

实现各种景观特征的计算算法，包括：
- 崎岖度计算（自相关函数）
- 多模态性分析（局部最优检测）
- 收敛状态评估
- 信息内容计算
- 多样性指标
"""

import numpy as np
import logging
from typing import List, Dict, Callable, Optional, Tuple, Any
from scipy.stats import pearsonr
from sklearn.cluster import DBSCAN
from collections import Counter
import math
import time

from ..models.data_structures import (
    LandscapeFeatures, PopulationState, SearchPhase, Individual, Fitness
)
from ..utils.incremental_stats import IncrementalStatistics


class FeatureCalculator:
    """
    景观特征计算器
    
    实现各种景观特征的高效计算算法。
    """
    
    def __init__(self, distance_function: Optional[Callable[[Individual, Individual], float]] = None):
        """
        初始化特征计算器
        
        Args:
            distance_function: 个体间距离计算函数
        """
        self.distance_function = distance_function or self._default_distance
        
        # 增量统计器
        self.fitness_stats = IncrementalStatistics()
        self.ruggedness_stats = IncrementalStatistics()
        
        # 历史数据缓存
        self.fitness_history = []
        self.autocorr_cache = {}
        
        # 配置参数
        self.max_history_size = 200
        self.autocorr_max_lag = 10
        self.modality_eps = 0.1
        self.modality_min_samples = 3
        
        self.logger = logging.getLogger(__name__)
    
    def calculate_all_features(self, solutions: List[Individual], 
                             fitnesses: np.ndarray,
                             current_population: PopulationState) -> LandscapeFeatures:
        """
        计算所有景观特征
        
        Args:
            solutions: 解个体列表
            fitnesses: 适应度数组
            current_population: 当前种群状态
            
        Returns:
            景观特征对象
        """
        start_time = time.time()
        
        try:
            # 基础验证
            if len(solutions) == 0 or len(fitnesses) == 0:
                self.logger.warning("输入数据为空，返回默认特征")
                return LandscapeFeatures()
            
            # 计算各项特征
            ruggedness, correlation_length = self._calculate_ruggedness(fitnesses)
            modality, local_optima_density = self._calculate_modality(solutions, fitnesses)
            convergence = self._calculate_convergence(fitnesses, current_population.generation)
            information_content = self._calculate_information_content(fitnesses)
            diversity_index = self._calculate_diversity_index(solutions)
            improvement_rate = self._calculate_improvement_rate()
            fitness_variance = float(np.var(fitnesses))
            search_phase = self._determine_search_phase(ruggedness, convergence, modality)
            
            # 计算置信度
            confidence = self._calculate_feature_confidence(len(solutions), fitness_variance)
            
            # 创建特征对象
            features = LandscapeFeatures(
                ruggedness=ruggedness,
                modality=modality,
                convergence=convergence,
                information_content=information_content,
                correlation_length=correlation_length,
                local_optima_density=local_optima_density,
                confidence=confidence,
                diversity_index=diversity_index,
                improvement_rate=improvement_rate,
                fitness_variance=fitness_variance,
                search_phase=search_phase
            )
            
            calculation_time = time.time() - start_time
            self.logger.debug(f"特征计算完成，耗时: {calculation_time*1000:.2f}ms")
            
            return features
            
        except Exception as e:
            self.logger.error(f"特征计算过程中出错: {e}")
            return LandscapeFeatures()
    
    def _calculate_ruggedness(self, fitnesses: np.ndarray) -> Tuple[float, float]:
        """
        计算景观崎岖度
        
        使用自相关函数: ρ(d) = Cov(f(s), f(s+d)) / Var(f(s))
        崎岖度 = 1 - |ρ(1)|
        相关长度 τ = -1 / ln|ρ(1)|
        
        Args:
            fitnesses: 适应度数组
            
        Returns:
            (ruggedness, correlation_length)
        """
        if len(fitnesses) < 2:
            return 0.5, float('inf')
        
        try:
            # 计算距离为1的自相关系数
            autocorr_1 = self._calculate_autocorrelation(fitnesses, lag=1)
            
            # 计算崎岖度
            ruggedness = 1.0 - abs(autocorr_1)
            
            # 计算相关长度
            if abs(autocorr_1) < 1e-10:
                correlation_length = float('inf')
            else:
                try:
                    correlation_length = -1.0 / np.log(abs(autocorr_1))
                    if correlation_length < 0:
                        correlation_length = float('inf')
                except (ValueError, ZeroDivisionError):
                    correlation_length = float('inf')
            
            # 更新统计
            self.ruggedness_stats.update(ruggedness)
            
            return float(ruggedness), float(correlation_length)
            
        except Exception as e:
            self.logger.warning(f"崎岖度计算出错: {e}")
            return 0.5, float('inf')
    
    def _calculate_autocorrelation(self, fitnesses: np.ndarray, lag: int = 1) -> float:
        """
        计算自相关函数
        
        Args:
            fitnesses: 适应度数组
            lag: 滞后距离
            
        Returns:
            自相关系数
        """
        if len(fitnesses) <= lag:
            return 0.0
        
        # 检查缓存
        cache_key = f"{len(fitnesses)}_{lag}_{hash(fitnesses.tobytes())}"
        if cache_key in self.autocorr_cache:
            return self.autocorr_cache[cache_key]
        
        try:
            # 计算滞后序列
            f_t = fitnesses[:-lag]
            f_t_lag = fitnesses[lag:]
            
            if len(f_t) == 0 or len(f_t_lag) == 0:
                return 0.0
            
            # 计算皮尔逊相关系数
            correlation, _ = pearsonr(f_t, f_t_lag)
            
            if np.isnan(correlation):
                correlation = 0.0
            
            # 缓存结果
            if len(self.autocorr_cache) < 100:  # 限制缓存大小
                self.autocorr_cache[cache_key] = correlation
            
            return float(correlation)
            
        except Exception as e:
            self.logger.warning(f"自相关计算出错: {e}")
            return 0.0
    
    def _calculate_modality(self, solutions: List[Individual], 
                          fitnesses: np.ndarray) -> Tuple[float, float]:
        """
        计算多模态性
        
        Args:
            solutions: 解个体列表
            fitnesses: 适应度数组
            
        Returns:
            (modality_score, local_optima_density)
        """
        if len(solutions) < self.modality_min_samples:
            return 0.0, 0.0
        
        try:
            # 检测局部最优解
            local_optima_indices = self._detect_local_optima(solutions, fitnesses)
            
            # 计算多模态性分数
            modality_score = len(local_optima_indices) / len(solutions)
            
            # 计算局部最优密度
            if len(solutions) > 0:
                local_optima_density = len(local_optima_indices) / len(solutions)
            else:
                local_optima_density = 0.0
            
            return float(modality_score), float(local_optima_density)
            
        except Exception as e:
            self.logger.warning(f"多模态性计算出错: {e}")
            return 0.0, 0.0
    
    def _detect_local_optima(self, solutions: List[Individual], 
                           fitnesses: np.ndarray) -> List[int]:
        """
        检测局部最优解
        
        Args:
            solutions: 解个体列表
            fitnesses: 适应度数组
            
        Returns:
            局部最优解的索引列表
        """
        local_optima = []
        
        for i, (solution, fitness) in enumerate(zip(solutions, fitnesses)):
            is_local_optimum = True
            
            # 检查邻域内是否有更好的解
            for j, (other_solution, other_fitness) in enumerate(zip(solutions, fitnesses)):
                if i != j:
                    distance = self.distance_function(solution, other_solution)
                    if distance <= self.modality_eps and other_fitness > fitness:
                        is_local_optimum = False
                        break
            
            if is_local_optimum:
                local_optima.append(i)
        
        return local_optima
    
    def _calculate_convergence(self, fitnesses: np.ndarray, generation: int) -> float:
        """
        计算收敛状态
        
        Convergence(t) = 1 - (σ_fitness(t) / σ_fitness(0))
        
        Args:
            fitnesses: 当前适应度数组
            generation: 当前代数
            
        Returns:
            收敛度 [0, 1]
        """
        current_variance = float(np.var(fitnesses))
        
        # 更新适应度统计
        for fitness in fitnesses:
            self.fitness_stats.update(fitness)
        
        # 获取初始方差（使用历史数据估计）
        if len(self.fitness_history) == 0:
            initial_variance = current_variance
        else:
            # 使用前几代的方差作为初始方差
            early_fitnesses = self.fitness_history[:min(50, len(self.fitness_history))]
            if len(early_fitnesses) > 0:
                initial_variance = np.var(early_fitnesses)
            else:
                initial_variance = current_variance
        
        # 计算收敛度
        if initial_variance < 1e-10:
            convergence = 1.0 if current_variance < 1e-10 else 0.0
        else:
            convergence = 1.0 - (current_variance / initial_variance)
            convergence = max(0.0, min(1.0, convergence))
        
        return float(convergence)
    
    def _calculate_information_content(self, fitnesses: np.ndarray) -> float:
        """
        计算信息内容
        
        基于适应度序列的符号化和熵计算
        
        Args:
            fitnesses: 适应度数组
            
        Returns:
            信息内容值
        """
        if len(fitnesses) < 2:
            return 0.0
        
        try:
            # 将适应度序列符号化
            symbols = self._discretize_fitnesses(fitnesses)
            
            if len(symbols) == 0:
                return 0.0
            
            # 计算不同块大小的熵
            max_block_size = min(6, len(symbols))
            entropies = []
            
            for block_size in range(1, max_block_size + 1):
                entropy = self._calculate_entropy(symbols, block_size)
                entropies.append(entropy)
            
            # 返回平均熵作为信息内容
            information_content = np.mean(entropies) if entropies else 0.0
            return float(information_content)
            
        except Exception as e:
            self.logger.warning(f"信息内容计算出错: {e}")
            return 0.0
    
    def _discretize_fitnesses(self, fitnesses: np.ndarray, precision: float = 0.01) -> List[str]:
        """将适应度值离散化为符号序列"""
        if len(fitnesses) < 2:
            return ['0']
        
        # 计算相邻适应度值的差异
        differences = np.diff(fitnesses)
        
        # 根据差异的符号和大小生成符号
        symbols = []
        for diff in differences:
            if abs(diff) < precision:
                symbols.append('=')  # 相等
            elif diff > 0:
                symbols.append('+')  # 增加
            else:
                symbols.append('-')  # 减少
        
        return symbols
    
    def _calculate_entropy(self, symbols: List[str], block_size: int = 1) -> float:
        """计算符号序列的熵"""
        if len(symbols) < block_size:
            return 0.0
        
        # 生成块序列
        blocks = []
        for i in range(len(symbols) - block_size + 1):
            block = ''.join(symbols[i:i + block_size])
            blocks.append(block)
        
        # 计算块的频率
        block_counts = Counter(blocks)
        total_blocks = len(blocks)
        
        # 计算熵
        entropy = 0.0
        for count in block_counts.values():
            probability = count / total_blocks
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def _calculate_diversity_index(self, solutions: List[Individual]) -> float:
        """
        计算多样性指数
        
        Args:
            solutions: 解个体列表
            
        Returns:
            多样性指数 [0, 1]
        """
        if len(solutions) < 2:
            return 0.0
        
        try:
            # 计算所有解对之间的平均距离
            total_distance = 0.0
            pair_count = 0
            
            for i in range(len(solutions)):
                for j in range(i + 1, len(solutions)):
                    distance = self.distance_function(solutions[i], solutions[j])
                    total_distance += distance
                    pair_count += 1
            
            if pair_count == 0:
                return 0.0
            
            average_distance = total_distance / pair_count
            
            # 归一化到[0, 1]范围
            # 这里假设最大距离为1.0，实际应用中可能需要调整
            diversity_index = min(1.0, average_distance)
            
            return float(diversity_index)
            
        except Exception as e:
            self.logger.warning(f"多样性指数计算出错: {e}")
            return 0.0
    
    def _calculate_improvement_rate(self, window_size: int = 10) -> float:
        """
        计算改善率
        
        Args:
            window_size: 计算窗口大小
            
        Returns:
            改善率
        """
        if len(self.fitness_history) < window_size:
            return 0.0
        
        try:
            recent_fitnesses = self.fitness_history[-window_size:]
            
            if len(recent_fitnesses) < 2:
                return 0.0
            
            # 计算线性趋势
            x = np.arange(len(recent_fitnesses))
            slope, _ = np.polyfit(x, recent_fitnesses, 1)
            
            return float(slope)
            
        except Exception as e:
            self.logger.warning(f"改善率计算出错: {e}")
            return 0.0
    
    def _determine_search_phase(self, ruggedness: float, convergence: float, 
                              modality: float) -> SearchPhase:
        """
        确定搜索阶段
        
        Args:
            ruggedness: 崎岖度
            convergence: 收敛度
            modality: 多模态性
            
        Returns:
            搜索阶段
        """
        # 基于特征值的简单规则
        if convergence < 0.3:
            return SearchPhase.EXPLORATION
        elif convergence > 0.7:
            return SearchPhase.EXPLOITATION
        else:
            return SearchPhase.TRANSITION
    
    def _calculate_feature_confidence(self, sample_size: int, variance: float) -> float:
        """
        计算特征置信度
        
        Args:
            sample_size: 样本大小
            variance: 适应度方差
            
        Returns:
            置信度 [0, 1]
        """
        # 基于样本大小的置信度
        size_confidence = min(1.0, sample_size / 50.0)
        
        # 基于方差的置信度（方差太小或太大都降低置信度）
        if variance < 1e-10:
            variance_confidence = 0.1  # 方差过小，可能数据质量不好
        else:
            variance_confidence = min(1.0, 1.0 / (1.0 + variance))
        
        # 综合置信度
        overall_confidence = (size_confidence + variance_confidence) / 2.0
        
        return float(max(0.0, min(1.0, overall_confidence)))
    
    def update_incremental(self, new_individuals: List[Individual], 
                          new_fitnesses: np.ndarray) -> None:
        """
        增量更新特征计算器
        
        Args:
            new_individuals: 新个体列表
            new_fitnesses: 新适应度数组
        """
        try:
            # 更新适应度历史
            self.fitness_history.extend(new_fitnesses.tolist())
            
            # 限制历史大小
            if len(self.fitness_history) > self.max_history_size:
                excess = len(self.fitness_history) - self.max_history_size
                self.fitness_history = self.fitness_history[excess:]
            
            # 更新统计器
            for fitness in new_fitnesses:
                self.fitness_stats.update(fitness)
            
            self.logger.debug(f"增量更新完成，新增样本: {len(new_individuals)}")
            
        except Exception as e:
            self.logger.error(f"增量更新出错: {e}")
    
    def reset(self) -> None:
        """重置特征计算器"""
        self.fitness_stats.reset()
        self.ruggedness_stats.reset()
        self.fitness_history.clear()
        self.autocorr_cache.clear()
        
        self.logger.info("特征计算器已重置")
    
    def _default_distance(self, individual1: Individual, individual2: Individual) -> float:
        """默认的个体距离计算函数"""
        try:
            if hasattr(individual1, '__len__') and hasattr(individual2, '__len__'):
                if len(individual1) != len(individual2):
                    return 1.0
                
                # 计算汉明距离的归一化版本
                differences = sum(1 for a, b in zip(individual1, individual2) if a != b)
                return differences / len(individual1) if len(individual1) > 0 else 0.0
            else:
                return 0.0 if individual1 == individual2 else 1.0
        except Exception:
            return 1.0
