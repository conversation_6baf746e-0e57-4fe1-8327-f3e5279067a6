"""
LLM接口模块

提供与大语言模型的标准化接口，支持多种LLM服务。
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union
import json
import aiohttp
import openai
from dataclasses import dataclass

from ..models.strategy_models import LLMResponse


@dataclass
class LLMConfig:
    """LLM配置"""
    model_name: str = "gpt-4"
    api_endpoint: str = "https://api.openai.com/v1/chat/completions"
    api_key: Optional[str] = None
    max_tokens: int = 2000
    temperature: float = 0.7
    timeout_seconds: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0


class LLMInterface:
    """
    LLM接口类
    
    提供统一的LLM调用接口，支持多种模型和服务商。
    """
    
    def __init__(self, config: LLMConfig):
        """
        初始化LLM接口
        
        Args:
            config: LLM配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化客户端
        self._init_clients()
        
        # 统计信息
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_tokens = 0
        self.total_response_time = 0.0
        
        self.logger.info(f"LLM接口初始化完成，模型: {config.model_name}")
    
    def _init_clients(self) -> None:
        """初始化各种LLM客户端"""
        try:
            # OpenAI客户端
            if self.config.api_key:
                openai.api_key = self.config.api_key
            
            # 其他客户端可以在这里添加
            
        except Exception as e:
            self.logger.warning(f"初始化LLM客户端时出现警告: {e}")
    
    async def generate_response(self, prompt: str, 
                              system_message: Optional[str] = None) -> LLMResponse:
        """
        生成LLM响应
        
        Args:
            prompt: 用户提示
            system_message: 系统消息
            
        Returns:
            LLM响应对象
        """
        self.total_requests += 1
        start_time = time.time()
        
        try:
            # 根据模型类型选择调用方法
            if "gpt" in self.config.model_name.lower():
                response = await self._call_openai(prompt, system_message)
            elif "claude" in self.config.model_name.lower():
                response = await self._call_anthropic(prompt, system_message)
            else:
                response = await self._call_generic(prompt, system_message)
            
            response_time = time.time() - start_time
            self.total_response_time += response_time
            self.successful_requests += 1
            
            # 创建响应对象
            llm_response = LLMResponse(
                raw_response=response.get("content", ""),
                model_name=self.config.model_name,
                response_time=response_time,
                token_count=response.get("token_count", 0)
            )
            
            self.total_tokens += llm_response.token_count
            
            self.logger.debug(f"LLM响应生成成功，耗时: {response_time:.2f}秒")
            return llm_response
            
        except Exception as e:
            self.failed_requests += 1
            self.logger.error(f"LLM响应生成失败: {e}")
            
            # 返回错误响应
            return LLMResponse(
                raw_response="",
                model_name=self.config.model_name,
                response_time=time.time() - start_time,
                parsing_errors=[str(e)]
            )
    
    async def _call_openai(self, prompt: str, 
                          system_message: Optional[str] = None) -> Dict[str, Any]:
        """调用OpenAI API"""
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": prompt})
        
        for attempt in range(self.config.retry_attempts):
            try:
                async with aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout_seconds)
                ) as session:
                    headers = {
                        "Authorization": f"Bearer {self.config.api_key}",
                        "Content-Type": "application/json"
                    }
                    
                    data = {
                        "model": self.config.model_name,
                        "messages": messages,
                        "max_tokens": self.config.max_tokens,
                        "temperature": self.config.temperature
                    }
                    
                    async with session.post(
                        self.config.api_endpoint,
                        headers=headers,
                        json=data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            content = result["choices"][0]["message"]["content"]
                            token_count = result.get("usage", {}).get("total_tokens", 0)
                            
                            return {
                                "content": content,
                                "token_count": token_count
                            }
                        else:
                            error_text = await response.text()
                            raise Exception(f"API调用失败: {response.status}, {error_text}")
            
            except Exception as e:
                if attempt < self.config.retry_attempts - 1:
                    self.logger.warning(f"OpenAI API调用失败，重试中... ({attempt + 1}/{self.config.retry_attempts}): {e}")
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                else:
                    raise e
    
    async def _call_anthropic(self, prompt: str, 
                            system_message: Optional[str] = None) -> Dict[str, Any]:
        """调用Anthropic Claude API"""
        # 这里是Claude API的调用逻辑
        # 由于Claude API的具体实现可能不同，这里提供一个框架
        
        full_prompt = prompt
        if system_message:
            full_prompt = f"{system_message}\n\n{prompt}"
        
        for attempt in range(self.config.retry_attempts):
            try:
                # 实际的Claude API调用逻辑
                # 这里需要根据Claude的具体API格式进行实现
                
                # 模拟响应（实际使用时需要替换为真实的API调用）
                await asyncio.sleep(0.1)  # 模拟网络延迟
                
                return {
                    "content": "Claude API响应内容",
                    "token_count": len(full_prompt.split())
                }
                
            except Exception as e:
                if attempt < self.config.retry_attempts - 1:
                    self.logger.warning(f"Claude API调用失败，重试中... ({attempt + 1}/{self.config.retry_attempts}): {e}")
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                else:
                    raise e
    
    async def _call_generic(self, prompt: str, 
                          system_message: Optional[str] = None) -> Dict[str, Any]:
        """调用通用API接口"""
        full_prompt = prompt
        if system_message:
            full_prompt = f"{system_message}\n\n{prompt}"
        
        for attempt in range(self.config.retry_attempts):
            try:
                async with aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout_seconds)
                ) as session:
                    
                    data = {
                        "model": self.config.model_name,
                        "prompt": full_prompt,
                        "max_tokens": self.config.max_tokens,
                        "temperature": self.config.temperature
                    }
                    
                    async with session.post(
                        self.config.api_endpoint,
                        json=data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            # 根据不同的API格式解析响应
                            content = self._extract_content(result)
                            token_count = self._extract_token_count(result)
                            
                            return {
                                "content": content,
                                "token_count": token_count
                            }
                        else:
                            error_text = await response.text()
                            raise Exception(f"API调用失败: {response.status}, {error_text}")
            
            except Exception as e:
                if attempt < self.config.retry_attempts - 1:
                    self.logger.warning(f"通用API调用失败，重试中... ({attempt + 1}/{self.config.retry_attempts}): {e}")
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                else:
                    raise e
    
    def _extract_content(self, response: Dict[str, Any]) -> str:
        """从响应中提取内容"""
        # 尝试多种可能的响应格式
        if "choices" in response and len(response["choices"]) > 0:
            choice = response["choices"][0]
            if "message" in choice:
                return choice["message"].get("content", "")
            elif "text" in choice:
                return choice["text"]
        
        if "content" in response:
            return response["content"]
        
        if "text" in response:
            return response["text"]
        
        return str(response)
    
    def _extract_token_count(self, response: Dict[str, Any]) -> int:
        """从响应中提取token数量"""
        if "usage" in response:
            return response["usage"].get("total_tokens", 0)
        
        if "token_count" in response:
            return response["token_count"]
        
        # 如果没有提供token数量，进行估算
        content = self._extract_content(response)
        return len(content.split())
    
    def generate_response_sync(self, prompt: str, 
                             system_message: Optional[str] = None) -> LLMResponse:
        """
        同步版本的响应生成
        
        Args:
            prompt: 用户提示
            system_message: 系统消息
            
        Returns:
            LLM响应对象
        """
        try:
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 运行异步方法
            return loop.run_until_complete(
                self.generate_response(prompt, system_message)
            )
            
        except Exception as e:
            self.logger.error(f"同步LLM调用失败: {e}")
            return LLMResponse(
                raw_response="",
                model_name=self.config.model_name,
                parsing_errors=[str(e)]
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        success_rate = self.successful_requests / max(1, self.total_requests)
        avg_response_time = self.total_response_time / max(1, self.successful_requests)
        avg_tokens_per_request = self.total_tokens / max(1, self.successful_requests)
        
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": success_rate,
            "total_tokens": self.total_tokens,
            "avg_response_time": avg_response_time,
            "avg_tokens_per_request": avg_tokens_per_request,
            "model_name": self.config.model_name
        }
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_tokens = 0
        self.total_response_time = 0.0
        
        self.logger.info("LLM接口统计信息已重置")


class MockLLMInterface(LLMInterface):
    """
    模拟LLM接口
    
    用于测试和开发环境，不需要真实的API调用。
    """
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.mock_responses = [
            {
                "global_strategy": "exploration",
                "strategy_parameters": {
                    "crossover_rate": 0.8,
                    "mutation_rate": 0.15,
                    "selection_pressure": 1.5,
                    "local_search_intensity": 0.3
                },
                "confidence": 0.75,
                "reasoning": "基于高崎岖度景观，建议增强探索能力",
                "expected_improvement": 0.12,
                "risk_assessment": 0.3
            },
            {
                "global_strategy": "exploitation",
                "strategy_parameters": {
                    "crossover_rate": 0.9,
                    "mutation_rate": 0.05,
                    "selection_pressure": 3.0,
                    "local_search_intensity": 0.8
                },
                "confidence": 0.85,
                "reasoning": "种群已收敛，建议加强局部搜索",
                "expected_improvement": 0.08,
                "risk_assessment": 0.15
            }
        ]
        self.response_index = 0
    
    async def generate_response(self, prompt: str, 
                              system_message: Optional[str] = None) -> LLMResponse:
        """生成模拟响应"""
        self.total_requests += 1
        start_time = time.time()
        
        # 模拟网络延迟
        await asyncio.sleep(0.5)
        
        try:
            # 选择模拟响应
            mock_data = self.mock_responses[self.response_index % len(self.mock_responses)]
            self.response_index += 1
            
            # 生成JSON响应
            response_content = json.dumps(mock_data, indent=2)
            
            response_time = time.time() - start_time
            self.total_response_time += response_time
            self.successful_requests += 1
            
            token_count = len(response_content.split())
            self.total_tokens += token_count
            
            return LLMResponse(
                raw_response=response_content,
                model_name=f"mock-{self.config.model_name}",
                response_time=response_time,
                token_count=token_count
            )
            
        except Exception as e:
            self.failed_requests += 1
            return LLMResponse(
                raw_response="",
                model_name=f"mock-{self.config.model_name}",
                response_time=time.time() - start_time,
                parsing_errors=[str(e)]
            )
