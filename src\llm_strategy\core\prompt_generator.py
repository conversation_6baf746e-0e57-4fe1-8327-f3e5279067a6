"""
提示生成器模块

负责将景观特征转换为自然语言提示，供LLM进行策略决策。
"""

import logging
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

from ..models.strategy_models import DecisionContext, StrategyType
from ...landscape_analysis.models.data_structures import LandscapeFeatures, SearchPhase


class PromptGenerator:
    """
    提示生成器
    
    将数值化的景观特征转换为结构化的自然语言提示。
    """
    
    def __init__(self, template_version: str = "v1.0"):
        """
        初始化提示生成器
        
        Args:
            template_version: 模板版本
        """
        self.template_version = template_version
        self.logger = logging.getLogger(__name__)
        
        # 特征描述映射
        self.feature_descriptions = {
            "ruggedness": {
                "low": "景观相对平滑，局部搜索效果较好",
                "medium": "景观具有中等崎岖度，需要平衡探索和开发",
                "high": "景观高度崎岖，需要强化全局探索能力"
            },
            "modality": {
                "low": "单模态或少模态景观，容易陷入局部最优",
                "medium": "中等多模态景观，存在多个局部最优区域",
                "high": "高度多模态景观，具有众多局部最优解"
            },
            "convergence": {
                "low": "种群分散，多样性较高，适合探索",
                "medium": "种群部分收敛，处于探索与开发的平衡点",
                "high": "种群高度收敛，需要增强多样性或局部搜索"
            },
            "information_content": {
                "low": "信息内容较低，结构相对简单",
                "medium": "信息内容中等，具有一定复杂性",
                "high": "信息内容丰富，结构复杂多样"
            }
        }
        
        # 搜索阶段描述
        self.phase_descriptions = {
            SearchPhase.EXPLORATION: "当前处于探索阶段，需要增强全局搜索能力",
            SearchPhase.EXPLOITATION: "当前处于开发阶段，应专注于局部优化",
            SearchPhase.TRANSITION: "当前处于过渡阶段，需要平衡探索与开发",
            SearchPhase.STAGNATION: "当前处于停滞阶段，需要引入扰动机制"
        }
        
        self.logger.info(f"提示生成器初始化完成，模板版本: {template_version}")
    
    def generate_strategy_prompt(self, context: DecisionContext) -> str:
        """
        生成策略选择提示
        
        Args:
            context: 决策上下文
            
        Returns:
            结构化的策略选择提示
        """
        try:
            # 构建提示的各个部分
            system_prompt = self._build_system_prompt()
            landscape_analysis = self._build_landscape_analysis(context.landscape_features)
            population_analysis = self._build_population_analysis(context.population_stats)
            historical_context = self._build_historical_context(
                context.performance_history, context.strategy_history
            )
            constraints_info = self._build_constraints_info(context)
            decision_request = self._build_decision_request(context)
            
            # 组合完整提示
            full_prompt = f"""
{system_prompt}

## 当前景观分析
{landscape_analysis}

## 种群状态分析
{population_analysis}

## 历史表现分析
{historical_context}

## 约束条件
{constraints_info}

## 决策请求
{decision_request}

请基于以上信息，提供详细的策略建议和参数配置。
"""
            
            self.logger.debug("策略选择提示生成完成")
            return full_prompt.strip()
            
        except Exception as e:
            self.logger.error(f"生成策略提示时出错: {e}")
            return self._generate_fallback_prompt(context)
    
    def _build_system_prompt(self) -> str:
        """构建系统提示"""
        return """你是一个专业的进化算法策略顾问，具有深厚的适应度景观分析和优化算法设计经验。

你的任务是基于当前的景观特征、种群状态和历史表现，为进化算法提供最优的策略建议。

请按照以下JSON格式返回你的建议：

```json
{
    "global_strategy": "exploration|exploitation|transition|hybrid",
    "strategy_parameters": {
        "crossover_rate": 0.8,
        "mutation_rate": 0.1,
        "selection_pressure": 2.0,
        "local_search_intensity": 0.5,
        "population_diversity_target": 0.3,
        "elite_preservation_rate": 0.1,
        "operator_weights": {
            "crossover": 0.4,
            "mutation": 0.3,
            "local_search": 0.2,
            "perturbation": 0.1
        }
    },
    "confidence": 0.85,
    "reasoning": "详细的推理过程和策略选择依据",
    "expected_improvement": 0.15,
    "risk_assessment": 0.2,
    "priority_operators": ["crossover", "mutation"],
    "avoid_operators": [],
    "special_instructions": {
        "focus_areas": ["diversity_maintenance", "local_optimization"],
        "adaptation_suggestions": "建议在接下来的5代中逐步调整变异率"
    }
}
```"""
    
    def _build_landscape_analysis(self, features: Dict[str, float]) -> str:
        """构建景观分析部分"""
        analysis_parts = []
        
        # 分析各个特征
        for feature_name, value in features.items():
            if feature_name in self.feature_descriptions:
                level = self._categorize_feature_value(value)
                description = self.feature_descriptions[feature_name][level]
                analysis_parts.append(f"- **{feature_name.title()}** ({value:.3f}): {description}")
        
        # 添加搜索阶段信息
        if "search_phase" in features:
            phase_value = features["search_phase"]
            # 假设搜索阶段以数值形式存储，需要转换
            phase = self._convert_phase_value(phase_value)
            if phase in self.phase_descriptions:
                analysis_parts.append(f"- **搜索阶段**: {self.phase_descriptions[phase]}")
        
        return "\n".join(analysis_parts)
    
    def _build_population_analysis(self, stats: Dict[str, float]) -> str:
        """构建种群分析部分"""
        analysis_parts = []
        
        # 适应度统计
        if "best_fitness" in stats and "mean_fitness" in stats:
            best_fitness = stats["best_fitness"]
            mean_fitness = stats["mean_fitness"]
            fitness_gap = best_fitness - mean_fitness
            analysis_parts.append(f"- **适应度分布**: 最佳={best_fitness:.6f}, 平均={mean_fitness:.6f}, 差距={fitness_gap:.6f}")
        
        # 多样性分析
        if "diversity" in stats:
            diversity = stats["diversity"]
            diversity_level = "高" if diversity > 0.6 else "中" if diversity > 0.3 else "低"
            analysis_parts.append(f"- **种群多样性**: {diversity:.3f} ({diversity_level})")
        
        # 收敛性分析
        if "convergence_rate" in stats:
            convergence = stats["convergence_rate"]
            convergence_level = "快速" if convergence > 0.7 else "中等" if convergence > 0.3 else "缓慢"
            analysis_parts.append(f"- **收敛速度**: {convergence:.3f} ({convergence_level})")
        
        # 种群大小
        if "population_size" in stats:
            pop_size = int(stats["population_size"])
            analysis_parts.append(f"- **种群规模**: {pop_size}个个体")
        
        return "\n".join(analysis_parts)
    
    def _build_historical_context(self, performance_history: List[float], 
                                strategy_history: List[Dict[str, Any]]) -> str:
        """构建历史上下文部分"""
        context_parts = []
        
        # 性能趋势分析
        if len(performance_history) >= 2:
            recent_trend = self._analyze_performance_trend(performance_history)
            context_parts.append(f"- **性能趋势**: {recent_trend}")
        
        # 策略历史分析
        if strategy_history:
            strategy_summary = self._analyze_strategy_history(strategy_history)
            context_parts.append(f"- **策略历史**: {strategy_summary}")
        
        # 改善率分析
        if len(performance_history) >= 5:
            improvement_rate = self._calculate_improvement_rate(performance_history)
            context_parts.append(f"- **近期改善率**: {improvement_rate:.3f}")
        
        return "\n".join(context_parts) if context_parts else "- 历史数据不足，基于当前状态进行决策"
    
    def _build_constraints_info(self, context: DecisionContext) -> str:
        """构建约束信息部分"""
        constraints_parts = []
        
        # 时间预算
        if context.time_budget:
            constraints_parts.append(f"- **时间预算**: {context.time_budget}秒")
        
        # 资源约束
        if context.resource_constraints:
            for resource, limit in context.resource_constraints.items():
                constraints_parts.append(f"- **{resource}约束**: {limit}")
        
        # 优化目标
        if context.optimization_objectives:
            objectives_str = ", ".join(context.optimization_objectives)
            constraints_parts.append(f"- **优化目标**: {objectives_str}")
        
        # 目标改善
        if context.target_improvement:
            constraints_parts.append(f"- **目标改善**: {context.target_improvement:.3f}")
        
        # 问题信息
        if context.problem_type != "unknown":
            constraints_parts.append(f"- **问题类型**: {context.problem_type}")
        
        if context.problem_size > 0:
            constraints_parts.append(f"- **问题规模**: {context.problem_size}")
        
        return "\n".join(constraints_parts) if constraints_parts else "- 无特殊约束条件"
    
    def _build_decision_request(self, context: DecisionContext) -> str:
        """构建决策请求部分"""
        return f"""
基于以上分析，请为第{context.generation}代提供策略建议：

1. **全局策略选择**: 选择最适合当前情况的策略类型
2. **参数配置**: 提供具体的算法参数设置
3. **算子权重**: 建议各种算子的使用比例
4. **风险评估**: 评估策略的潜在风险
5. **预期效果**: 预测策略的改善效果
6. **特殊指导**: 提供具体的执行建议

请确保你的建议：
- 基于景观特征的科学分析
- 考虑历史表现和趋势
- 平衡探索与开发的需求
- 适应当前的约束条件
- 提供可执行的具体指导
"""
    
    def _categorize_feature_value(self, value: float) -> str:
        """将特征值分类为低/中/高"""
        if value < 0.33:
            return "low"
        elif value < 0.67:
            return "medium"
        else:
            return "high"
    
    def _convert_phase_value(self, phase_value: float) -> SearchPhase:
        """将数值转换为搜索阶段枚举"""
        if phase_value < 0.25:
            return SearchPhase.EXPLORATION
        elif phase_value < 0.5:
            return SearchPhase.TRANSITION
        elif phase_value < 0.75:
            return SearchPhase.EXPLOITATION
        else:
            return SearchPhase.STAGNATION
    
    def _analyze_performance_trend(self, history: List[float]) -> str:
        """分析性能趋势"""
        if len(history) < 2:
            return "数据不足"
        
        recent_values = history[-5:]  # 最近5个值
        
        if len(recent_values) >= 2:
            trend = recent_values[-1] - recent_values[0]
            if trend > 0.01:
                return "持续改善"
            elif trend < -0.01:
                return "性能下降"
            else:
                return "基本稳定"
        
        return "趋势不明"
    
    def _analyze_strategy_history(self, history: List[Dict[str, Any]]) -> str:
        """分析策略历史"""
        if not history:
            return "无历史策略记录"
        
        recent_strategies = history[-3:]  # 最近3个策略
        strategy_types = [s.get("global_strategy", "unknown") for s in recent_strategies]
        
        if len(set(strategy_types)) == 1:
            return f"持续使用{strategy_types[0]}策略"
        else:
            return f"策略变化: {' -> '.join(strategy_types)}"
    
    def _calculate_improvement_rate(self, history: List[float]) -> float:
        """计算改善率"""
        if len(history) < 2:
            return 0.0
        
        recent_window = min(5, len(history))
        recent_values = history[-recent_window:]
        
        if len(recent_values) >= 2:
            initial = recent_values[0]
            final = recent_values[-1]
            
            if initial != 0:
                return (final - initial) / abs(initial)
        
        return 0.0
    
    def _generate_fallback_prompt(self, context: DecisionContext) -> str:
        """生成备用提示"""
        return f"""
作为进化算法专家，请为第{context.generation}代提供策略建议。

当前状态：
- 问题类型: {context.problem_type}
- 问题规模: {context.problem_size}
- 优化目标: {', '.join(context.optimization_objectives)}

请提供JSON格式的策略建议，包括全局策略、参数配置和执行指导。
"""
    
    def generate_parameter_tuning_prompt(self, current_params: Dict[str, float],
                                       performance_feedback: Dict[str, float]) -> str:
        """
        生成参数调优提示
        
        Args:
            current_params: 当前参数设置
            performance_feedback: 性能反馈
            
        Returns:
            参数调优提示
        """
        return f"""
## 参数调优请求

### 当前参数设置
{json.dumps(current_params, indent=2)}

### 性能反馈
{json.dumps(performance_feedback, indent=2)}

请基于性能反馈，建议参数调整方案。返回JSON格式的调整建议：

```json
{{
    "parameter_adjustments": {{
        "crossover_rate": 0.85,
        "mutation_rate": 0.12
    }},
    "adjustment_reasoning": "基于性能反馈的调整理由",
    "expected_impact": "预期的性能影响",
    "confidence": 0.8
}}
```
"""
