"""
增量统计计算模块

实现高效的增量统计计算算法，避免重复计算历史数据。
使用Welford算法进行在线方差计算。
"""

import math
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging


class IncrementalStatistics:
    """
    增量统计计算器
    
    使用Welford算法实现在线统计计算，支持：
    - 均值、方差、标准差的增量更新
    - 最小值、最大值跟踪
    - 样本数量统计
    """
    
    def __init__(self):
        """初始化统计计算器"""
        self.reset()
        self.logger = logging.getLogger(__name__)
    
    def reset(self) -> None:
        """重置所有统计量"""
        self.count = 0
        self.mean = 0.0
        self.m2 = 0.0  # 平方差的累积
        self.min_value = float('inf')
        self.max_value = float('-inf')
    
    def update(self, value: float) -> None:
        """
        更新统计量（Welford算法）
        
        Args:
            value: 新的数值
        """
        if not isinstance(value, (int, float)) or math.isnan(value):
            self.logger.warning(f"忽略无效值: {value}")
            return
        
        self.count += 1
        delta = value - self.mean
        self.mean += delta / self.count
        delta2 = value - self.mean
        self.m2 += delta * delta2
        
        # 更新最值
        self.min_value = min(self.min_value, value)
        self.max_value = max(self.max_value, value)
    
    def update_batch(self, values: List[float]) -> None:
        """
        批量更新统计量
        
        Args:
            values: 数值列表
        """
        for value in values:
            self.update(value)
    
    def get_variance(self, ddof: int = 1) -> float:
        """
        获取方差
        
        Args:
            ddof: 自由度修正，默认为1（样本方差）
            
        Returns:
            方差值
        """
        if self.count <= ddof:
            return 0.0
        return self.m2 / (self.count - ddof)
    
    def get_std(self, ddof: int = 1) -> float:
        """
        获取标准差
        
        Args:
            ddof: 自由度修正，默认为1（样本标准差）
            
        Returns:
            标准差值
        """
        return math.sqrt(self.get_variance(ddof))
    
    def get_coefficient_of_variation(self) -> float:
        """
        获取变异系数（CV = std/mean）
        
        Returns:
            变异系数
        """
        if abs(self.mean) < 1e-10:
            return 0.0
        return self.get_std() / abs(self.mean)
    
    def get_statistics(self) -> Dict[str, float]:
        """
        获取所有统计量
        
        Returns:
            包含所有统计量的字典
        """
        return {
            "count": self.count,
            "mean": self.mean,
            "variance": self.get_variance(),
            "std": self.get_std(),
            "min": self.min_value if self.count > 0 else 0.0,
            "max": self.max_value if self.count > 0 else 0.0,
            "range": (self.max_value - self.min_value) if self.count > 0 else 0.0,
            "cv": self.get_coefficient_of_variation()
        }
    
    def merge(self, other: 'IncrementalStatistics') -> 'IncrementalStatistics':
        """
        合并两个统计计算器
        
        Args:
            other: 另一个统计计算器
            
        Returns:
            合并后的新统计计算器
        """
        if self.count == 0:
            return other
        if other.count == 0:
            return self
        
        merged = IncrementalStatistics()
        merged.count = self.count + other.count
        
        # 合并均值
        merged.mean = (self.count * self.mean + other.count * other.mean) / merged.count
        
        # 合并方差（使用并行算法）
        delta = other.mean - self.mean
        merged.m2 = (self.m2 + other.m2 + 
                    delta * delta * self.count * other.count / merged.count)
        
        # 合并最值
        merged.min_value = min(self.min_value, other.min_value)
        merged.max_value = max(self.max_value, other.max_value)
        
        return merged
    
    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_statistics()
        return (f"IncrementalStats(count={stats['count']}, "
                f"mean={stats['mean']:.4f}, std={stats['std']:.4f}, "
                f"range=[{stats['min']:.4f}, {stats['max']:.4f}])")


class MovingStatistics:
    """
    移动窗口统计计算器
    
    维护固定大小窗口内的统计信息。
    """
    
    def __init__(self, window_size: int = 50):
        """
        初始化移动统计计算器
        
        Args:
            window_size: 窗口大小
        """
        self.window_size = max(1, window_size)
        self.values: List[float] = []
        self.stats = IncrementalStatistics()
        self.logger = logging.getLogger(__name__)
    
    def update(self, value: float) -> None:
        """
        更新移动统计
        
        Args:
            value: 新的数值
        """
        if not isinstance(value, (int, float)) or math.isnan(value):
            self.logger.warning(f"忽略无效值: {value}")
            return
        
        self.values.append(value)
        
        # 如果超出窗口大小，移除最旧的值
        if len(self.values) > self.window_size:
            self.values.pop(0)
        
        # 重新计算统计量
        self.stats.reset()
        self.stats.update_batch(self.values)
    
    def get_statistics(self) -> Dict[str, float]:
        """获取当前窗口的统计量"""
        return self.stats.get_statistics()
    
    def get_trend(self, min_points: int = 3) -> float:
        """
        计算趋势（线性回归斜率）
        
        Args:
            min_points: 计算趋势所需的最少点数
            
        Returns:
            趋势斜率
        """
        if len(self.values) < min_points:
            return 0.0
        
        x = np.arange(len(self.values))
        y = np.array(self.values)
        
        try:
            slope, _ = np.polyfit(x, y, 1)
            return float(slope)
        except Exception as e:
            self.logger.warning(f"计算趋势时出错: {e}")
            return 0.0
    
    def is_stable(self, cv_threshold: float = 0.1) -> bool:
        """
        判断数据是否稳定（基于变异系数）
        
        Args:
            cv_threshold: 变异系数阈值
            
        Returns:
            是否稳定
        """
        if len(self.values) < 3:
            return False
        
        cv = self.stats.get_coefficient_of_variation()
        return cv < cv_threshold
    
    def __len__(self) -> int:
        """返回当前窗口中的数据点数量"""
        return len(self.values)


class MultiVariateStatistics:
    """
    多变量统计计算器
    
    同时跟踪多个变量的统计信息。
    """
    
    def __init__(self, variable_names: List[str]):
        """
        初始化多变量统计计算器
        
        Args:
            variable_names: 变量名称列表
        """
        self.variable_names = variable_names
        self.stats = {name: IncrementalStatistics() for name in variable_names}
        self.logger = logging.getLogger(__name__)
    
    def update(self, values: Dict[str, float]) -> None:
        """
        更新多变量统计
        
        Args:
            values: 变量值字典
        """
        for name, value in values.items():
            if name in self.stats:
                self.stats[name].update(value)
            else:
                self.logger.warning(f"未知变量: {name}")
    
    def get_statistics(self, variable_name: str = None) -> Dict[str, Dict[str, float]]:
        """
        获取统计信息
        
        Args:
            variable_name: 特定变量名，如果为None则返回所有变量
            
        Returns:
            统计信息字典
        """
        if variable_name:
            if variable_name in self.stats:
                return {variable_name: self.stats[variable_name].get_statistics()}
            else:
                return {}
        
        return {name: stat.get_statistics() for name, stat in self.stats.items()}
    
    def get_correlation_matrix(self) -> np.ndarray:
        """
        计算变量间的相关系数矩阵
        
        Returns:
            相关系数矩阵
        """
        # 这里简化实现，实际应用中可能需要更复杂的在线相关计算
        n_vars = len(self.variable_names)
        correlation_matrix = np.eye(n_vars)
        
        # 注意：这里只是示例，实际的在线相关计算需要更复杂的算法
        self.logger.info("相关系数矩阵计算需要历史数据，当前返回单位矩阵")
        
        return correlation_matrix
