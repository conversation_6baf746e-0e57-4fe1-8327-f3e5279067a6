"""
策略模型数据结构

定义LLM策略选择系统中使用的所有数据结构。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import time
import json


class StrategyType(Enum):
    """策略类型枚举"""
    EXPLORATION = "exploration"
    EXPLOITATION = "exploitation"
    TRANSITION = "transition"
    HYBRID = "hybrid"


class OperatorType(Enum):
    """算子类型枚举"""
    CROSSOVER = "crossover"
    MUTATION = "mutation"
    SELECTION = "selection"
    LOCAL_SEARCH = "local_search"
    PERTURBATION = "perturbation"


class ConfidenceLevel(Enum):
    """置信度等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class StrategyParameters:
    """策略参数数据结构"""
    # 基础参数
    crossover_rate: float = 0.8
    mutation_rate: float = 0.1
    selection_pressure: float = 2.0
    local_search_intensity: float = 0.5
    
    # 扩展参数
    population_diversity_target: float = 0.3
    elite_preservation_rate: float = 0.1
    adaptive_parameter_enabled: bool = True
    
    # 算子特定参数
    operator_weights: Dict[str, float] = field(default_factory=lambda: {
        "crossover": 0.4,
        "mutation": 0.3,
        "local_search": 0.2,
        "perturbation": 0.1
    })
    
    # 动态调整参数
    parameter_adaptation_rate: float = 0.1
    parameter_bounds: Dict[str, tuple] = field(default_factory=lambda: {
        "crossover_rate": (0.1, 0.95),
        "mutation_rate": (0.01, 0.5),
        "selection_pressure": (1.0, 5.0),
        "local_search_intensity": (0.1, 1.0)
    })
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "crossover_rate": self.crossover_rate,
            "mutation_rate": self.mutation_rate,
            "selection_pressure": self.selection_pressure,
            "local_search_intensity": self.local_search_intensity,
            "population_diversity_target": self.population_diversity_target,
            "elite_preservation_rate": self.elite_preservation_rate,
            "adaptive_parameter_enabled": self.adaptive_parameter_enabled,
            "operator_weights": self.operator_weights,
            "parameter_adaptation_rate": self.parameter_adaptation_rate,
            "parameter_bounds": self.parameter_bounds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StrategyParameters':
        """从字典创建实例"""
        return cls(
            crossover_rate=data.get("crossover_rate", 0.8),
            mutation_rate=data.get("mutation_rate", 0.1),
            selection_pressure=data.get("selection_pressure", 2.0),
            local_search_intensity=data.get("local_search_intensity", 0.5),
            population_diversity_target=data.get("population_diversity_target", 0.3),
            elite_preservation_rate=data.get("elite_preservation_rate", 0.1),
            adaptive_parameter_enabled=data.get("adaptive_parameter_enabled", True),
            operator_weights=data.get("operator_weights", {
                "crossover": 0.4, "mutation": 0.3, "local_search": 0.2, "perturbation": 0.1
            }),
            parameter_adaptation_rate=data.get("parameter_adaptation_rate", 0.1),
            parameter_bounds=data.get("parameter_bounds", {
                "crossover_rate": (0.1, 0.95),
                "mutation_rate": (0.01, 0.5),
                "selection_pressure": (1.0, 5.0),
                "local_search_intensity": (0.1, 1.0)
            })
        )
    
    def validate(self) -> bool:
        """验证参数有效性"""
        try:
            # 检查基础参数范围
            if not (0.0 <= self.crossover_rate <= 1.0):
                return False
            if not (0.0 <= self.mutation_rate <= 1.0):
                return False
            if not (self.selection_pressure > 0):
                return False
            if not (0.0 <= self.local_search_intensity <= 1.0):
                return False
            
            # 检查算子权重总和
            total_weight = sum(self.operator_weights.values())
            if abs(total_weight - 1.0) > 0.01:
                return False
            
            return True
        except Exception:
            return False


@dataclass
class StrategyDecision:
    """策略决策结果数据结构"""
    # 全局策略
    global_strategy: StrategyType
    strategy_parameters: StrategyParameters
    
    # 个体级策略
    individual_strategies: Dict[int, Dict[str, float]] = field(default_factory=dict)
    
    # 决策元信息
    confidence: float = 0.0
    reasoning: str = ""
    expected_improvement: float = 0.0
    risk_assessment: float = 0.0
    
    # 时间信息
    decision_time: float = field(default_factory=time.time)
    valid_until: Optional[float] = None
    
    # 执行指导
    priority_operators: List[str] = field(default_factory=list)
    avoid_operators: List[str] = field(default_factory=list)
    special_instructions: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "global_strategy": self.global_strategy.value,
            "strategy_parameters": self.strategy_parameters.to_dict(),
            "individual_strategies": self.individual_strategies,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "expected_improvement": self.expected_improvement,
            "risk_assessment": self.risk_assessment,
            "decision_time": self.decision_time,
            "valid_until": self.valid_until,
            "priority_operators": self.priority_operators,
            "avoid_operators": self.avoid_operators,
            "special_instructions": self.special_instructions
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StrategyDecision':
        """从字典创建实例"""
        global_strategy = StrategyType(data.get("global_strategy", "exploration"))
        strategy_parameters = StrategyParameters.from_dict(
            data.get("strategy_parameters", {})
        )
        
        return cls(
            global_strategy=global_strategy,
            strategy_parameters=strategy_parameters,
            individual_strategies=data.get("individual_strategies", {}),
            confidence=data.get("confidence", 0.0),
            reasoning=data.get("reasoning", ""),
            expected_improvement=data.get("expected_improvement", 0.0),
            risk_assessment=data.get("risk_assessment", 0.0),
            decision_time=data.get("decision_time", time.time()),
            valid_until=data.get("valid_until"),
            priority_operators=data.get("priority_operators", []),
            avoid_operators=data.get("avoid_operators", []),
            special_instructions=data.get("special_instructions", {})
        )
    
    def is_valid(self) -> bool:
        """检查决策是否仍然有效"""
        if self.valid_until is None:
            return True
        return time.time() < self.valid_until
    
    def get_confidence_level(self) -> ConfidenceLevel:
        """获取置信度等级"""
        if self.confidence >= 0.8:
            return ConfidenceLevel.VERY_HIGH
        elif self.confidence >= 0.6:
            return ConfidenceLevel.HIGH
        elif self.confidence >= 0.4:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW


@dataclass
class DecisionContext:
    """决策上下文数据结构"""
    # 景观特征
    landscape_features: Dict[str, float]
    
    # 种群状态
    population_stats: Dict[str, float]
    
    # 历史信息
    performance_history: List[float] = field(default_factory=list)
    strategy_history: List[Dict[str, Any]] = field(default_factory=list)
    
    # 约束条件
    time_budget: Optional[float] = None
    resource_constraints: Dict[str, float] = field(default_factory=dict)
    
    # 目标信息
    optimization_objectives: List[str] = field(default_factory=lambda: ["fitness"])
    target_improvement: float = 0.1
    
    # 环境信息
    problem_type: str = "unknown"
    problem_size: int = 0
    generation: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "landscape_features": self.landscape_features,
            "population_stats": self.population_stats,
            "performance_history": self.performance_history,
            "strategy_history": self.strategy_history,
            "time_budget": self.time_budget,
            "resource_constraints": self.resource_constraints,
            "optimization_objectives": self.optimization_objectives,
            "target_improvement": self.target_improvement,
            "problem_type": self.problem_type,
            "problem_size": self.problem_size,
            "generation": self.generation
        }
    
    def get_landscape_summary(self) -> str:
        """获取景观特征摘要"""
        features = self.landscape_features
        
        ruggedness = features.get("ruggedness", 0.5)
        modality = features.get("modality", 0.5)
        convergence = features.get("convergence", 0.5)
        
        if ruggedness > 0.7:
            landscape_desc = "高度崎岖"
        elif ruggedness > 0.4:
            landscape_desc = "中等崎岖"
        else:
            landscape_desc = "相对平滑"
        
        if modality > 0.6:
            modality_desc = "多模态"
        elif modality > 0.3:
            modality_desc = "中等模态"
        else:
            modality_desc = "单模态"
        
        if convergence > 0.7:
            convergence_desc = "高收敛"
        elif convergence > 0.3:
            convergence_desc = "中等收敛"
        else:
            convergence_desc = "低收敛"
        
        return f"{landscape_desc}、{modality_desc}、{convergence_desc}的景观"


@dataclass
class LLMResponse:
    """LLM响应数据结构"""
    # 原始响应
    raw_response: str
    
    # 解析结果
    parsed_decision: Optional[StrategyDecision] = None
    parsing_success: bool = False
    parsing_errors: List[str] = field(default_factory=list)
    
    # 响应质量评估
    response_quality: float = 0.0
    completeness_score: float = 0.0
    consistency_score: float = 0.0
    
    # 元信息
    model_name: str = ""
    response_time: float = 0.0
    token_count: int = 0
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "raw_response": self.raw_response,
            "parsed_decision": self.parsed_decision.to_dict() if self.parsed_decision else None,
            "parsing_success": self.parsing_success,
            "parsing_errors": self.parsing_errors,
            "response_quality": self.response_quality,
            "completeness_score": self.completeness_score,
            "consistency_score": self.consistency_score,
            "model_name": self.model_name,
            "response_time": self.response_time,
            "token_count": self.token_count,
            "timestamp": self.timestamp
        }
    
    def is_usable(self) -> bool:
        """判断响应是否可用"""
        return (
            self.parsing_success and 
            self.parsed_decision is not None and
            self.response_quality > 0.3
        )


@dataclass
class StrategyExecutionResult:
    """策略执行结果数据结构"""
    # 执行信息
    strategy_decision: StrategyDecision
    execution_success: bool = False
    execution_time: float = 0.0
    
    # 性能结果
    fitness_improvement: float = 0.0
    diversity_change: float = 0.0
    convergence_change: float = 0.0
    
    # 执行详情
    applied_operators: List[str] = field(default_factory=list)
    parameter_adjustments: Dict[str, float] = field(default_factory=dict)
    execution_log: List[str] = field(default_factory=list)
    
    # 评估指标
    strategy_effectiveness: float = 0.0
    parameter_optimality: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "strategy_decision": self.strategy_decision.to_dict(),
            "execution_success": self.execution_success,
            "execution_time": self.execution_time,
            "fitness_improvement": self.fitness_improvement,
            "diversity_change": self.diversity_change,
            "convergence_change": self.convergence_change,
            "applied_operators": self.applied_operators,
            "parameter_adjustments": self.parameter_adjustments,
            "execution_log": self.execution_log,
            "strategy_effectiveness": self.strategy_effectiveness,
            "parameter_optimality": self.parameter_optimality
        }


# 类型别名
FeatureVector = Dict[str, float]
ParameterVector = Dict[str, float]
StrategyHistory = List[Dict[str, Any]]
PerformanceMetrics = Dict[str, float]
