"""
数据结构定义模块

定义景观分析系统中使用的所有数据结构和类型。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
import numpy as np
from enum import Enum
import time


class SearchPhase(Enum):
    """搜索阶段枚举"""
    EXPLORATION = "exploration"
    TRANSITION = "transition"
    EXPLOITATION = "exploitation"


class TriggerType(Enum):
    """触发类型枚举"""
    PERFORMANCE_STAGNATION = "performance_stagnation"
    DIVERSITY_DROP = "diversity_drop"
    GENERATION_INTERVAL = "generation_interval"
    STRATEGY_FAILURE = "strategy_failure"


@dataclass
class LandscapeFeatures:
    """景观特征数据结构"""
    ruggedness: float = 0.0
    modality: float = 0.0
    convergence: float = 0.0
    information_content: float = 0.0
    correlation_length: float = float('inf')
    local_optima_density: float = 0.0
    confidence: float = 0.0
    timestamp: float = field(default_factory=time.time)
    
    # 扩展特征
    diversity_index: float = 0.0
    improvement_rate: float = 0.0
    fitness_variance: float = 0.0
    search_phase: SearchPhase = SearchPhase.EXPLORATION
    
    def to_dict(self) -> Dict[str, Union[float, str]]:
        """转换为字典格式"""
        return {
            'ruggedness': self.ruggedness,
            'modality': self.modality,
            'convergence': self.convergence,
            'information_content': self.information_content,
            'correlation_length': self.correlation_length,
            'local_optima_density': self.local_optima_density,
            'confidence': self.confidence,
            'timestamp': self.timestamp,
            'diversity_index': self.diversity_index,
            'improvement_rate': self.improvement_rate,
            'fitness_variance': self.fitness_variance,
            'search_phase': self.search_phase.value
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LandscapeFeatures':
        """从字典创建实例"""
        search_phase = SearchPhase(data.get('search_phase', 'exploration'))
        return cls(
            ruggedness=data.get('ruggedness', 0.0),
            modality=data.get('modality', 0.0),
            convergence=data.get('convergence', 0.0),
            information_content=data.get('information_content', 0.0),
            correlation_length=data.get('correlation_length', float('inf')),
            local_optima_density=data.get('local_optima_density', 0.0),
            confidence=data.get('confidence', 0.0),
            timestamp=data.get('timestamp', time.time()),
            diversity_index=data.get('diversity_index', 0.0),
            improvement_rate=data.get('improvement_rate', 0.0),
            fitness_variance=data.get('fitness_variance', 0.0),
            search_phase=search_phase
        )


@dataclass
class PopulationState:
    """种群状态数据结构"""
    individuals: List[Any]
    fitnesses: np.ndarray
    generation: int
    
    # 基础统计信息
    best_fitness: float = field(init=False)
    worst_fitness: float = field(init=False)
    mean_fitness: float = field(init=False)
    std_fitness: float = field(init=False)
    diversity: float = 0.0
    
    # 扩展信息
    population_size: int = field(init=False)
    fitness_range: float = field(init=False)
    
    def __post_init__(self):
        """初始化后处理"""
        if len(self.fitnesses) > 0:
            self.best_fitness = float(np.max(self.fitnesses))
            self.worst_fitness = float(np.min(self.fitnesses))
            self.mean_fitness = float(np.mean(self.fitnesses))
            self.std_fitness = float(np.std(self.fitnesses))
            self.fitness_range = self.best_fitness - self.worst_fitness
        else:
            self.best_fitness = 0.0
            self.worst_fitness = 0.0
            self.mean_fitness = 0.0
            self.std_fitness = 0.0
            self.fitness_range = 0.0
        
        self.population_size = len(self.individuals)
    
    def get_fitness_percentile(self, percentile: float) -> float:
        """获取适应度百分位数"""
        if len(self.fitnesses) == 0:
            return 0.0
        return float(np.percentile(self.fitnesses, percentile))
    
    def get_top_individuals(self, k: int) -> List[Any]:
        """获取前k个最优个体"""
        if len(self.individuals) == 0:
            return []
        
        k = min(k, len(self.individuals))
        top_indices = np.argsort(self.fitnesses)[-k:][::-1]
        return [self.individuals[i] for i in top_indices]


@dataclass
class TriggerCondition:
    """触发条件数据结构"""
    trigger_type: TriggerType
    threshold: float
    window_size: int = 10
    min_interval: int = 5
    enabled: bool = True
    
    # 状态信息
    last_triggered_generation: int = -1
    trigger_count: int = 0
    
    def should_trigger(self, current_value: float, generation: int) -> bool:
        """判断是否应该触发"""
        if not self.enabled:
            return False
        
        # 检查最小间隔
        if generation - self.last_triggered_generation < self.min_interval:
            return False
        
        # 根据触发类型判断
        if self.trigger_type == TriggerType.PERFORMANCE_STAGNATION:
            return current_value < self.threshold
        elif self.trigger_type == TriggerType.DIVERSITY_DROP:
            return current_value > self.threshold  # 多样性下降率大于阈值
        else:
            return current_value > self.threshold
    
    def mark_triggered(self, generation: int) -> None:
        """标记已触发"""
        self.last_triggered_generation = generation
        self.trigger_count += 1


@dataclass
class AnalysisResult:
    """分析结果数据结构"""
    landscape_features: LandscapeFeatures
    population_state: PopulationState
    trigger_info: Dict[str, Any]
    analysis_time: float
    confidence_score: float
    
    # 历史比较信息
    feature_trends: Dict[str, float] = field(default_factory=dict)
    anomaly_detected: bool = False
    anomaly_description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'landscape_features': self.landscape_features.to_dict(),
            'population_stats': {
                'generation': self.population_state.generation,
                'population_size': self.population_state.population_size,
                'best_fitness': self.population_state.best_fitness,
                'mean_fitness': self.population_state.mean_fitness,
                'std_fitness': self.population_state.std_fitness,
                'diversity': self.population_state.diversity
            },
            'trigger_info': self.trigger_info,
            'analysis_time': self.analysis_time,
            'confidence_score': self.confidence_score,
            'feature_trends': self.feature_trends,
            'anomaly_detected': self.anomaly_detected,
            'anomaly_description': self.anomaly_description
        }


@dataclass
class HistoryRecord:
    """历史记录数据结构"""
    generation: int
    landscape_features: LandscapeFeatures
    population_stats: Dict[str, float]
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'generation': self.generation,
            'landscape_features': self.landscape_features.to_dict(),
            'population_stats': self.population_stats,
            'timestamp': self.timestamp
        }


@dataclass
class CacheConfig:
    """缓存配置数据结构"""
    max_history_size: int = 100
    max_window_size: int = 50
    cleanup_threshold: float = 0.8  # 当缓存使用率超过此值时进行清理
    retention_policy: str = "fifo"  # fifo, lru, quality_based
    
    # 置信度相关配置
    min_confidence_threshold: float = 0.3
    confidence_decay_rate: float = 0.95
    
    def validate(self) -> bool:
        """验证配置有效性"""
        return (
            self.max_history_size > 0 and
            self.max_window_size > 0 and
            0.0 < self.cleanup_threshold < 1.0 and
            self.retention_policy in ["fifo", "lru", "quality_based"] and
            0.0 <= self.min_confidence_threshold <= 1.0 and
            0.0 < self.confidence_decay_rate <= 1.0
        )


# 类型别名定义
Individual = Any
Fitness = float
Generation = int
Distance = float
Probability = float
