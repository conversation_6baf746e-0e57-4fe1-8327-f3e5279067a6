"""
策略选择器模块

整合景观分析结果和LLM决策，提供智能的策略选择功能。
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
import numpy as np

from .prompt_generator import PromptGenerator
from .llm_interface import LLMInterface, LLMConfig, MockLLMInterface
from ..utils.response_parser import ResponseParser
from ..models.strategy_models import (
    StrategyDecision, DecisionContext, LLMResponse, StrategyExecutionResult
)
from ...landscape_analysis.models.data_structures import AnalysisResult


class StrategySelector:
    """
    策略选择器
    
    整合景观分析和LLM决策，提供智能的策略选择服务。
    """
    
    def __init__(self, 
                 llm_config: LLMConfig,
                 use_mock_llm: bool = False,
                 enable_caching: bool = True):
        """
        初始化策略选择器
        
        Args:
            llm_config: LLM配置
            use_mock_llm: 是否使用模拟LLM
            enable_caching: 是否启用缓存
        """
        self.llm_config = llm_config
        self.enable_caching = enable_caching
        
        # 初始化组件
        self.prompt_generator = PromptGenerator()
        
        if use_mock_llm:
            self.llm_interface = MockLLMInterface(llm_config)
        else:
            self.llm_interface = LLMInterface(llm_config)
        
        self.response_parser = ResponseParser(validation_enabled=True)
        
        # 决策缓存
        self.decision_cache: Dict[str, StrategyDecision] = {}
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        
        # 执行历史
        self.execution_history: List[StrategyExecutionResult] = []
        self.decision_history: List[StrategyDecision] = []
        
        # 性能统计
        self.total_decisions = 0
        self.successful_decisions = 0
        self.total_decision_time = 0.0
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("策略选择器初始化完成")
    
    async def select_strategy(self, analysis_result: AnalysisResult) -> StrategyDecision:
        """
        选择策略
        
        Args:
            analysis_result: 景观分析结果
            
        Returns:
            策略决策
        """
        self.total_decisions += 1
        start_time = time.time()
        
        try:
            # 构建决策上下文
            context = self._build_decision_context(analysis_result)
            
            # 检查缓存
            if self.enable_caching:
                cached_decision = self._check_cache(context)
                if cached_decision:
                    self.cache_hit_count += 1
                    self.logger.debug("使用缓存的策略决策")
                    return cached_decision
            
            self.cache_miss_count += 1
            
            # 生成提示
            prompt = self.prompt_generator.generate_strategy_prompt(context)
            
            # 调用LLM
            llm_response = await self.llm_interface.generate_response(prompt)
            
            # 解析响应
            parsed_response = self.response_parser.parse_response(llm_response)
            
            # 获取策略决策
            if parsed_response.is_usable():
                strategy_decision = parsed_response.parsed_decision
                self.successful_decisions += 1
            else:
                # 使用备用决策
                self.logger.warning("LLM响应不可用，使用备用决策")
                strategy_decision = self.response_parser.create_fallback_decision({
                    "generation": context.generation,
                    "landscape_features": context.landscape_features
                })
            
            # 后处理决策
            strategy_decision = self._post_process_decision(strategy_decision, context)
            
            # 缓存决策
            if self.enable_caching:
                self._cache_decision(context, strategy_decision)
            
            # 记录历史
            self.decision_history.append(strategy_decision)
            if len(self.decision_history) > 100:  # 限制历史大小
                self.decision_history.pop(0)
            
            decision_time = time.time() - start_time
            self.total_decision_time += decision_time
            
            self.logger.info(f"策略选择完成 - 策略: {strategy_decision.global_strategy.value}, "
                           f"置信度: {strategy_decision.confidence:.3f}, 耗时: {decision_time:.2f}秒")
            
            return strategy_decision
            
        except Exception as e:
            self.logger.error(f"策略选择失败: {e}")
            # 返回备用决策
            return self.response_parser.create_fallback_decision({
                "generation": getattr(analysis_result.population_state, 'generation', 0)
            })
    
    def select_strategy_sync(self, analysis_result: AnalysisResult) -> StrategyDecision:
        """
        同步版本的策略选择
        
        Args:
            analysis_result: 景观分析结果
            
        Returns:
            策略决策
        """
        try:
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 运行异步方法
            return loop.run_until_complete(self.select_strategy(analysis_result))
            
        except Exception as e:
            self.logger.error(f"同步策略选择失败: {e}")
            return self.response_parser.create_fallback_decision({
                "generation": getattr(analysis_result.population_state, 'generation', 0)
            })
    
    def _build_decision_context(self, analysis_result: AnalysisResult) -> DecisionContext:
        """构建决策上下文"""
        # 提取景观特征
        features = analysis_result.landscape_features
        landscape_features = {
            "ruggedness": features.ruggedness,
            "modality": features.modality,
            "convergence": features.convergence,
            "information_content": features.information_content,
            "search_phase": features.search_phase.value
        }
        
        # 提取种群统计
        pop_state = analysis_result.population_state
        population_stats = {
            "best_fitness": pop_state.best_fitness,
            "mean_fitness": pop_state.mean_fitness,
            "std_fitness": pop_state.std_fitness,
            "diversity": pop_state.diversity,
            "population_size": pop_state.population_size
        }
        
        # 构建性能历史
        performance_history = []
        if len(self.decision_history) > 0:
            # 从执行历史中提取性能数据
            for result in self.execution_history[-10:]:  # 最近10次
                performance_history.append(result.fitness_improvement)
        
        # 构建策略历史
        strategy_history = []
        for decision in self.decision_history[-5:]:  # 最近5次决策
            strategy_history.append({
                "global_strategy": decision.global_strategy.value,
                "confidence": decision.confidence,
                "expected_improvement": decision.expected_improvement
            })
        
        return DecisionContext(
            landscape_features=landscape_features,
            population_stats=population_stats,
            performance_history=performance_history,
            strategy_history=strategy_history,
            generation=pop_state.generation,
            problem_type="optimization",  # 可以根据实际情况调整
            problem_size=pop_state.population_size
        )
    
    def _check_cache(self, context: DecisionContext) -> Optional[StrategyDecision]:
        """检查决策缓存"""
        # 生成缓存键
        cache_key = self._generate_cache_key(context)
        
        if cache_key in self.decision_cache:
            cached_decision = self.decision_cache[cache_key]
            
            # 检查决策是否仍然有效
            if cached_decision.is_valid():
                return cached_decision
            else:
                # 删除过期的缓存
                del self.decision_cache[cache_key]
        
        return None
    
    def _cache_decision(self, context: DecisionContext, decision: StrategyDecision) -> None:
        """缓存决策"""
        cache_key = self._generate_cache_key(context)
        
        # 设置有效期（例如10代）
        decision.valid_until = time.time() + 600  # 10分钟有效期
        
        self.decision_cache[cache_key] = decision
        
        # 限制缓存大小
        if len(self.decision_cache) > 50:
            # 删除最旧的缓存项
            oldest_key = min(self.decision_cache.keys(), 
                           key=lambda k: self.decision_cache[k].decision_time)
            del self.decision_cache[oldest_key]
    
    def _generate_cache_key(self, context: DecisionContext) -> str:
        """生成缓存键"""
        # 基于关键特征生成缓存键
        key_features = [
            f"gen_{context.generation // 5}",  # 按5代分组
            f"rug_{context.landscape_features.get('ruggedness', 0):.1f}",
            f"mod_{context.landscape_features.get('modality', 0):.1f}",
            f"conv_{context.landscape_features.get('convergence', 0):.1f}",
            f"div_{context.population_stats.get('diversity', 0):.1f}"
        ]
        
        return "_".join(key_features)
    
    def _post_process_decision(self, decision: StrategyDecision, 
                             context: DecisionContext) -> StrategyDecision:
        """后处理决策"""
        # 基于历史表现调整置信度
        if len(self.execution_history) > 0:
            recent_results = self.execution_history[-5:]
            avg_effectiveness = np.mean([r.strategy_effectiveness for r in recent_results])
            
            # 如果最近的策略效果不好，降低置信度
            if avg_effectiveness < 0.3:
                decision.confidence *= 0.8
        
        # 基于景观特征调整参数
        ruggedness = context.landscape_features.get("ruggedness", 0.5)
        modality = context.landscape_features.get("modality", 0.5)
        
        # 高崎岖度时增加探索
        if ruggedness > 0.7:
            decision.strategy_parameters.mutation_rate = min(
                decision.strategy_parameters.mutation_rate * 1.2, 0.3
            )
        
        # 高多模态时增加多样性保持
        if modality > 0.6:
            decision.strategy_parameters.population_diversity_target = max(
                decision.strategy_parameters.population_diversity_target, 0.4
            )
        
        return decision
    
    def record_execution_result(self, result: StrategyExecutionResult) -> None:
        """记录执行结果"""
        self.execution_history.append(result)
        
        # 限制历史大小
        if len(self.execution_history) > 100:
            self.execution_history.pop(0)
        
        self.logger.debug(f"记录策略执行结果 - 效果: {result.strategy_effectiveness:.3f}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        success_rate = self.successful_decisions / max(1, self.total_decisions)
        cache_hit_rate = self.cache_hit_count / max(1, self.cache_hit_count + self.cache_miss_count)
        avg_decision_time = self.total_decision_time / max(1, self.successful_decisions)
        
        # LLM统计
        llm_stats = self.llm_interface.get_statistics()
        
        return {
            "total_decisions": self.total_decisions,
            "successful_decisions": self.successful_decisions,
            "success_rate": success_rate,
            "cache_hit_rate": cache_hit_rate,
            "avg_decision_time": avg_decision_time,
            "cache_size": len(self.decision_cache),
            "execution_history_size": len(self.execution_history),
            "llm_statistics": llm_stats
        }
    
    def get_recent_decisions(self, count: int = 10) -> List[StrategyDecision]:
        """获取最近的决策"""
        return self.decision_history[-count:]
    
    def get_recent_results(self, count: int = 10) -> List[StrategyExecutionResult]:
        """获取最近的执行结果"""
        return self.execution_history[-count:]
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.decision_cache.clear()
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        self.logger.info("策略选择器缓存已清空")
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.total_decisions = 0
        self.successful_decisions = 0
        self.total_decision_time = 0.0
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        
        # 重置LLM统计
        self.llm_interface.reset_statistics()
        
        self.logger.info("策略选择器统计信息已重置")
    
    async def batch_select_strategies(self, 
                                    analysis_results: List[AnalysisResult]) -> List[StrategyDecision]:
        """
        批量选择策略
        
        Args:
            analysis_results: 分析结果列表
            
        Returns:
            策略决策列表
        """
        tasks = [self.select_strategy(result) for result in analysis_results]
        decisions = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        valid_decisions = []
        for i, decision in enumerate(decisions):
            if isinstance(decision, Exception):
                self.logger.error(f"批量策略选择第{i}项失败: {decision}")
                # 使用备用决策
                fallback = self.response_parser.create_fallback_decision({
                    "generation": getattr(analysis_results[i].population_state, 'generation', 0)
                })
                valid_decisions.append(fallback)
            else:
                valid_decisions.append(decision)
        
        return valid_decisions
