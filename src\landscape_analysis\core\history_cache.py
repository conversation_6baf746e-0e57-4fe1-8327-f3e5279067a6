"""
历史缓存管理模块

实现高效的历史数据缓存和检索机制，包括：
- LRU缓存策略
- 相似性匹配
- 自动清理机制
- 统计信息跟踪
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from collections import OrderedDict
import numpy as np
import hashlib
import json

from ..models.data_structures import (
    AnalysisResult, LandscapeFeatures, HistoryRecord, CacheConfig
)


class HistoryCache:
    """
    历史缓存管理器
    
    使用LRU策略管理分析结果的缓存，支持相似性匹配和自动清理。
    """
    
    def __init__(self, max_size: int = 200, config: Optional[CacheConfig] = None):
        """
        初始化历史缓存
        
        Args:
            max_size: 最大缓存大小
            config: 缓存配置
        """
        self.max_size = max_size
        self.config = config or CacheConfig()
        
        # 验证配置
        if not self.config.validate():
            self.logger.warning("缓存配置无效，使用默认配置")
            self.config = CacheConfig()
        
        # LRU缓存实现
        self.cache: OrderedDict[str, AnalysisResult] = OrderedDict()
        self.feature_history: List[LandscapeFeatures] = []
        self.confidence_history: List[float] = []
        
        # 统计信息
        self.hit_count = 0
        self.miss_count = 0
        self.total_queries = 0
        self.cleanup_count = 0
        self.last_cleanup_time = time.time()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"历史缓存初始化完成，最大大小: {max_size}")
    
    def add_analysis(self, analysis_result: AnalysisResult) -> None:
        """
        添加分析结果到缓存
        
        Args:
            analysis_result: 分析结果
        """
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(analysis_result)
            
            # 添加到LRU缓存
            self.cache[cache_key] = analysis_result
            
            # 移动到末尾（最近使用）
            self.cache.move_to_end(cache_key)
            
            # 添加到特征历史
            self.feature_history.append(analysis_result.landscape_features)
            self.confidence_history.append(analysis_result.confidence_score)
            
            # 限制历史大小
            if len(self.feature_history) > self.config.max_history_size:
                self.feature_history.pop(0)
                self.confidence_history.pop(0)
            
            # 检查是否需要清理
            if len(self.cache) > self.max_size:
                self._cleanup_cache()
            
            self.logger.debug(f"添加分析结果到缓存，键: {cache_key}")
            
        except Exception as e:
            self.logger.error(f"添加分析结果到缓存时出错: {e}")
    
    def get_similar_analysis(self, generation: int, 
                           fitness_signature: str,
                           similarity_threshold: float = 0.8) -> Optional[AnalysisResult]:
        """
        获取相似的分析结果
        
        Args:
            generation: 当前代数
            fitness_signature: 适应度签名
            similarity_threshold: 相似度阈值
            
        Returns:
            相似的分析结果，如果没有找到则返回None
        """
        self.total_queries += 1
        
        try:
            # 首先尝试精确匹配
            exact_key = f"{generation}_{fitness_signature}"
            if exact_key in self.cache:
                self.hit_count += 1
                # 移动到末尾（最近使用）
                self.cache.move_to_end(exact_key)
                self.logger.debug(f"缓存精确命中: {exact_key}")
                return self.cache[exact_key]
            
            # 尝试相似性匹配
            best_match = None
            best_similarity = 0.0
            
            for cache_key, analysis_result in self.cache.items():
                similarity = self._calculate_similarity(
                    generation, fitness_signature, cache_key, analysis_result
                )
                
                if similarity > best_similarity and similarity >= similarity_threshold:
                    best_similarity = similarity
                    best_match = analysis_result
            
            if best_match:
                self.hit_count += 1
                self.logger.debug(f"缓存相似性命中，相似度: {best_similarity:.3f}")
                return best_match
            else:
                self.miss_count += 1
                return None
                
        except Exception as e:
            self.logger.error(f"获取相似分析结果时出错: {e}")
            self.miss_count += 1
            return None
    
    def get_recent_features(self, window_size: int = 10) -> List[LandscapeFeatures]:
        """
        获取最近的景观特征
        
        Args:
            window_size: 窗口大小
            
        Returns:
            最近的景观特征列表
        """
        if len(self.feature_history) == 0:
            return []
        
        window_size = min(window_size, len(self.feature_history))
        return self.feature_history[-window_size:]
    
    def get_recent_confidences(self, window_size: int = 10) -> List[float]:
        """
        获取最近的置信度
        
        Args:
            window_size: 窗口大小
            
        Returns:
            最近的置信度列表
        """
        if len(self.confidence_history) == 0:
            return []
        
        window_size = min(window_size, len(self.confidence_history))
        return self.confidence_history[-window_size:]
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        hit_rate = self.hit_count / max(1, self.total_queries)
        
        return {
            "cache_size": len(self.cache),
            "max_size": self.max_size,
            "utilization": len(self.cache) / self.max_size,
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "total_queries": self.total_queries,
            "hit_rate": hit_rate,
            "cleanup_count": self.cleanup_count,
            "feature_history_size": len(self.feature_history),
            "confidence_history_size": len(self.confidence_history)
        }
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.feature_history.clear()
        self.confidence_history.clear()
        
        # 重置统计信息
        self.hit_count = 0
        self.miss_count = 0
        self.total_queries = 0
        self.cleanup_count = 0
        
        self.logger.info("历史缓存已清空")
    
    def _generate_cache_key(self, analysis_result: AnalysisResult) -> str:
        """
        生成缓存键
        
        Args:
            analysis_result: 分析结果
            
        Returns:
            缓存键字符串
        """
        try:
            # 提取关键信息
            generation = analysis_result.population_state.generation
            best_fitness = analysis_result.population_state.best_fitness
            mean_fitness = analysis_result.population_state.mean_fitness
            std_fitness = analysis_result.population_state.std_fitness
            population_size = analysis_result.population_state.population_size
            
            # 创建签名字符串
            signature_data = {
                "generation": generation,
                "best_fitness": round(best_fitness, 3),
                "mean_fitness": round(mean_fitness, 3),
                "std_fitness": round(std_fitness, 3),
                "population_size": population_size
            }
            
            # 生成哈希
            signature_str = json.dumps(signature_data, sort_keys=True)
            cache_key = hashlib.md5(signature_str.encode()).hexdigest()[:16]
            
            return f"{generation}_{cache_key}"
            
        except Exception as e:
            self.logger.warning(f"生成缓存键时出错: {e}")
            return f"{time.time()}_{hash(str(analysis_result))}"
    
    def _calculate_similarity(self, generation: int, fitness_signature: str,
                            cache_key: str, cached_result: AnalysisResult) -> float:
        """
        计算相似度
        
        Args:
            generation: 当前代数
            fitness_signature: 适应度签名
            cache_key: 缓存键
            cached_result: 缓存的分析结果
            
        Returns:
            相似度分数 [0, 1]
        """
        try:
            similarity_factors = []
            
            # 代数相似度
            cached_generation = cached_result.population_state.generation
            generation_diff = abs(generation - cached_generation)
            generation_similarity = max(0.0, 1.0 - generation_diff / 50.0)
            similarity_factors.append(generation_similarity)
            
            # 适应度分布相似度
            current_parts = fitness_signature.split('_')
            cached_signature = self._extract_fitness_signature(cached_result)
            cached_parts = cached_signature.split('_')
            
            if len(current_parts) >= 2 and len(cached_parts) >= 2:
                try:
                    current_mean = float(current_parts[0])
                    current_std = float(current_parts[1])
                    cached_mean = float(cached_parts[0])
                    cached_std = float(cached_parts[1])
                    
                    mean_similarity = max(0.0, 1.0 - abs(current_mean - cached_mean) / max(abs(current_mean), abs(cached_mean), 1.0))
                    std_similarity = max(0.0, 1.0 - abs(current_std - cached_std) / max(current_std, cached_std, 1.0))
                    
                    similarity_factors.append(mean_similarity)
                    similarity_factors.append(std_similarity)
                except ValueError:
                    pass
            
            # 置信度相似度
            confidence_similarity = max(0.0, 1.0 - abs(cached_result.confidence_score - 0.5) / 0.5)
            similarity_factors.append(confidence_similarity)
            
            # 综合相似度
            overall_similarity = np.mean(similarity_factors) if similarity_factors else 0.0
            
            return float(overall_similarity)
            
        except Exception as e:
            self.logger.warning(f"计算相似度时出错: {e}")
            return 0.0
    
    def _extract_fitness_signature(self, analysis_result: AnalysisResult) -> str:
        """从分析结果中提取适应度签名"""
        try:
            mean_fitness = analysis_result.population_state.mean_fitness
            std_fitness = analysis_result.population_state.std_fitness
            population_size = analysis_result.population_state.population_size
            
            return f"{mean_fitness:.3f}_{std_fitness:.3f}_{population_size}"
        except Exception:
            return "unknown"
    
    def _cleanup_cache(self) -> None:
        """清理缓存"""
        try:
            self.cleanup_count += 1
            self.last_cleanup_time = time.time()
            
            # 计算需要删除的数量
            target_size = int(self.max_size * (1.0 - self.config.cleanup_threshold))
            items_to_remove = len(self.cache) - target_size
            
            if items_to_remove <= 0:
                return
            
            # 根据清理策略删除项目
            if self.config.retention_policy == "fifo":
                self._cleanup_fifo(items_to_remove)
            elif self.config.retention_policy == "lru":
                self._cleanup_lru(items_to_remove)
            elif self.config.retention_policy == "quality_based":
                self._cleanup_quality_based(items_to_remove)
            
            self.logger.info(f"缓存清理完成，删除 {items_to_remove} 项，当前大小: {len(self.cache)}")
            
        except Exception as e:
            self.logger.error(f"缓存清理时出错: {e}")
    
    def _cleanup_fifo(self, items_to_remove: int) -> None:
        """FIFO清理策略"""
        for _ in range(items_to_remove):
            if self.cache:
                self.cache.popitem(last=False)  # 删除最旧的项目
    
    def _cleanup_lru(self, items_to_remove: int) -> None:
        """LRU清理策略"""
        for _ in range(items_to_remove):
            if self.cache:
                self.cache.popitem(last=False)  # 删除最少使用的项目
    
    def _cleanup_quality_based(self, items_to_remove: int) -> None:
        """基于质量的清理策略"""
        if not self.cache:
            return
        
        # 按置信度排序，删除置信度最低的项目
        items_by_confidence = []
        for key, result in self.cache.items():
            items_by_confidence.append((key, result.confidence_score))
        
        # 按置信度升序排序
        items_by_confidence.sort(key=lambda x: x[1])
        
        # 删除置信度最低的项目
        for i in range(min(items_to_remove, len(items_by_confidence))):
            key_to_remove = items_by_confidence[i][0]
            if key_to_remove in self.cache:
                del self.cache[key_to_remove]
    
    def get_feature_trends(self, feature_name: str, window_size: int = 10) -> List[float]:
        """
        获取特定特征的趋势数据
        
        Args:
            feature_name: 特征名称
            window_size: 窗口大小
            
        Returns:
            特征值列表
        """
        if len(self.feature_history) == 0:
            return []
        
        window_size = min(window_size, len(self.feature_history))
        recent_features = self.feature_history[-window_size:]
        
        try:
            values = []
            for features in recent_features:
                if hasattr(features, feature_name):
                    values.append(getattr(features, feature_name))
            return values
        except Exception as e:
            self.logger.warning(f"获取特征趋势时出错: {e}")
            return []
    
    def analyze_cache_performance(self) -> Dict[str, Any]:
        """
        分析缓存性能
        
        Returns:
            性能分析结果
        """
        stats = self.get_cache_statistics()
        
        # 计算性能指标
        performance_analysis = {
            "efficiency": {
                "hit_rate": stats["hit_rate"],
                "utilization": stats["utilization"],
                "cleanup_frequency": self.cleanup_count / max(1, self.total_queries)
            },
            "recommendations": []
        }
        
        # 生成建议
        if stats["hit_rate"] < 0.3:
            performance_analysis["recommendations"].append("命中率较低，考虑调整相似度阈值")
        
        if stats["utilization"] > 0.9:
            performance_analysis["recommendations"].append("缓存利用率过高，考虑增加缓存大小")
        
        if self.cleanup_count > self.total_queries * 0.1:
            performance_analysis["recommendations"].append("清理频率过高，考虑优化清理策略")
        
        return performance_analysis
