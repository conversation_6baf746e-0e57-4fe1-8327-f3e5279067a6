"""
响应解析器模块

负责解析LLM的响应，提取策略决策信息。
"""

import json
import re
import logging
from typing import Dict, List, Optional, Any, Tuple
import numpy as np

from ..models.strategy_models import (
    StrategyDecision, StrategyParameters, StrategyType, LLMResponse
)


class ResponseParser:
    """
    响应解析器
    
    解析LLM的自然语言响应，提取结构化的策略决策信息。
    """
    
    def __init__(self, validation_enabled: bool = True):
        """
        初始化响应解析器
        
        Args:
            validation_enabled: 是否启用验证
        """
        self.validation_enabled = validation_enabled
        self.logger = logging.getLogger(__name__)
        
        # 默认参数值
        self.default_parameters = StrategyParameters()
        
        # 策略类型映射
        self.strategy_type_mapping = {
            "exploration": StrategyType.EXPLORATION,
            "exploitation": StrategyType.EXPLOITATION,
            "transition": StrategyType.TRANSITION,
            "hybrid": StrategyType.HYBRID,
            "explore": StrategyType.EXPLORATION,
            "exploit": StrategyType.EXPLOITATION,
            "balance": StrategyType.TRANSITION
        }
        
        self.logger.info("响应解析器初始化完成")
    
    def parse_response(self, llm_response: LLMResponse) -> LLMResponse:
        """
        解析LLM响应
        
        Args:
            llm_response: 原始LLM响应
            
        Returns:
            包含解析结果的LLM响应
        """
        try:
            # 提取JSON内容
            json_content = self._extract_json_content(llm_response.raw_response)
            
            if not json_content:
                llm_response.parsing_errors.append("未找到有效的JSON内容")
                return llm_response
            
            # 解析JSON
            parsed_data = json.loads(json_content)
            
            # 创建策略决策对象
            strategy_decision = self._create_strategy_decision(parsed_data)
            
            # 验证决策
            if self.validation_enabled:
                validation_errors = self._validate_strategy_decision(strategy_decision)
                if validation_errors:
                    llm_response.parsing_errors.extend(validation_errors)
                    # 如果验证失败，尝试修复
                    strategy_decision = self._fix_strategy_decision(strategy_decision)
            
            # 评估响应质量
            quality_scores = self._evaluate_response_quality(parsed_data, llm_response.raw_response)
            
            # 更新响应对象
            llm_response.parsed_decision = strategy_decision
            llm_response.parsing_success = len(llm_response.parsing_errors) == 0
            llm_response.response_quality = quality_scores["overall"]
            llm_response.completeness_score = quality_scores["completeness"]
            llm_response.consistency_score = quality_scores["consistency"]
            
            self.logger.debug(f"响应解析完成，质量分数: {llm_response.response_quality:.3f}")
            
        except json.JSONDecodeError as e:
            llm_response.parsing_errors.append(f"JSON解析错误: {e}")
            self.logger.warning(f"JSON解析失败: {e}")
            
        except Exception as e:
            llm_response.parsing_errors.append(f"解析过程出错: {e}")
            self.logger.error(f"响应解析出错: {e}")
        
        return llm_response
    
    def _extract_json_content(self, raw_response: str) -> Optional[str]:
        """从原始响应中提取JSON内容"""
        # 尝试多种JSON提取模式
        patterns = [
            r'```json\s*(.*?)\s*```',  # 标准markdown代码块
            r'```\s*(.*?)\s*```',      # 通用代码块
            r'\{.*\}',                 # 直接的JSON对象
            r'\[.*\]'                  # JSON数组
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, raw_response, re.DOTALL | re.IGNORECASE)
            if matches:
                # 选择最长的匹配（通常是最完整的）
                json_candidate = max(matches, key=len).strip()
                
                # 验证是否为有效JSON
                try:
                    json.loads(json_candidate)
                    return json_candidate
                except json.JSONDecodeError:
                    continue
        
        # 如果没有找到标准格式，尝试直接解析整个响应
        try:
            json.loads(raw_response.strip())
            return raw_response.strip()
        except json.JSONDecodeError:
            pass
        
        return None
    
    def _create_strategy_decision(self, parsed_data: Dict[str, Any]) -> StrategyDecision:
        """从解析的数据创建策略决策对象"""
        # 解析全局策略
        global_strategy_str = parsed_data.get("global_strategy", "exploration").lower()
        global_strategy = self.strategy_type_mapping.get(
            global_strategy_str, StrategyType.EXPLORATION
        )
        
        # 解析策略参数
        strategy_params_data = parsed_data.get("strategy_parameters", {})
        strategy_parameters = self._parse_strategy_parameters(strategy_params_data)
        
        # 解析个体级策略
        individual_strategies = parsed_data.get("individual_strategies", {})
        
        # 解析元信息
        confidence = float(parsed_data.get("confidence", 0.5))
        reasoning = parsed_data.get("reasoning", "")
        expected_improvement = float(parsed_data.get("expected_improvement", 0.0))
        risk_assessment = float(parsed_data.get("risk_assessment", 0.5))
        
        # 解析执行指导
        priority_operators = parsed_data.get("priority_operators", [])
        avoid_operators = parsed_data.get("avoid_operators", [])
        special_instructions = parsed_data.get("special_instructions", {})
        
        return StrategyDecision(
            global_strategy=global_strategy,
            strategy_parameters=strategy_parameters,
            individual_strategies=individual_strategies,
            confidence=confidence,
            reasoning=reasoning,
            expected_improvement=expected_improvement,
            risk_assessment=risk_assessment,
            priority_operators=priority_operators,
            avoid_operators=avoid_operators,
            special_instructions=special_instructions
        )
    
    def _parse_strategy_parameters(self, params_data: Dict[str, Any]) -> StrategyParameters:
        """解析策略参数"""
        # 使用默认参数作为基础
        params = StrategyParameters()
        
        # 更新基础参数
        if "crossover_rate" in params_data:
            params.crossover_rate = float(params_data["crossover_rate"])
        
        if "mutation_rate" in params_data:
            params.mutation_rate = float(params_data["mutation_rate"])
        
        if "selection_pressure" in params_data:
            params.selection_pressure = float(params_data["selection_pressure"])
        
        if "local_search_intensity" in params_data:
            params.local_search_intensity = float(params_data["local_search_intensity"])
        
        # 更新扩展参数
        if "population_diversity_target" in params_data:
            params.population_diversity_target = float(params_data["population_diversity_target"])
        
        if "elite_preservation_rate" in params_data:
            params.elite_preservation_rate = float(params_data["elite_preservation_rate"])
        
        if "adaptive_parameter_enabled" in params_data:
            params.adaptive_parameter_enabled = bool(params_data["adaptive_parameter_enabled"])
        
        # 更新算子权重
        if "operator_weights" in params_data:
            operator_weights = params_data["operator_weights"]
            if isinstance(operator_weights, dict):
                params.operator_weights.update(operator_weights)
        
        # 更新动态调整参数
        if "parameter_adaptation_rate" in params_data:
            params.parameter_adaptation_rate = float(params_data["parameter_adaptation_rate"])
        
        if "parameter_bounds" in params_data:
            parameter_bounds = params_data["parameter_bounds"]
            if isinstance(parameter_bounds, dict):
                params.parameter_bounds.update(parameter_bounds)
        
        return params
    
    def _validate_strategy_decision(self, decision: StrategyDecision) -> List[str]:
        """验证策略决策的有效性"""
        errors = []
        
        # 验证策略参数
        if not decision.strategy_parameters.validate():
            errors.append("策略参数验证失败")
        
        # 验证置信度范围
        if not (0.0 <= decision.confidence <= 1.0):
            errors.append(f"置信度超出范围: {decision.confidence}")
        
        # 验证风险评估范围
        if not (0.0 <= decision.risk_assessment <= 1.0):
            errors.append(f"风险评估超出范围: {decision.risk_assessment}")
        
        # 验证预期改善范围
        if not (-1.0 <= decision.expected_improvement <= 1.0):
            errors.append(f"预期改善超出合理范围: {decision.expected_improvement}")
        
        # 验证算子权重
        operator_weights = decision.strategy_parameters.operator_weights
        total_weight = sum(operator_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            errors.append(f"算子权重总和不为1: {total_weight}")
        
        return errors
    
    def _fix_strategy_decision(self, decision: StrategyDecision) -> StrategyDecision:
        """修复策略决策中的问题"""
        # 修复置信度
        decision.confidence = np.clip(decision.confidence, 0.0, 1.0)
        
        # 修复风险评估
        decision.risk_assessment = np.clip(decision.risk_assessment, 0.0, 1.0)
        
        # 修复预期改善
        decision.expected_improvement = np.clip(decision.expected_improvement, -1.0, 1.0)
        
        # 修复算子权重
        operator_weights = decision.strategy_parameters.operator_weights
        total_weight = sum(operator_weights.values())
        if total_weight > 0:
            for key in operator_weights:
                operator_weights[key] /= total_weight
        else:
            # 如果权重全为0，使用默认权重
            decision.strategy_parameters.operator_weights = self.default_parameters.operator_weights.copy()
        
        # 修复参数范围
        params = decision.strategy_parameters
        params.crossover_rate = np.clip(params.crossover_rate, 0.0, 1.0)
        params.mutation_rate = np.clip(params.mutation_rate, 0.0, 1.0)
        params.selection_pressure = max(1.0, params.selection_pressure)
        params.local_search_intensity = np.clip(params.local_search_intensity, 0.0, 1.0)
        params.population_diversity_target = np.clip(params.population_diversity_target, 0.0, 1.0)
        params.elite_preservation_rate = np.clip(params.elite_preservation_rate, 0.0, 1.0)
        
        return decision
    
    def _evaluate_response_quality(self, parsed_data: Dict[str, Any], 
                                 raw_response: str) -> Dict[str, float]:
        """评估响应质量"""
        scores = {
            "completeness": 0.0,
            "consistency": 0.0,
            "clarity": 0.0,
            "overall": 0.0
        }
        
        # 完整性评分
        required_fields = [
            "global_strategy", "strategy_parameters", "confidence", 
            "reasoning", "expected_improvement", "risk_assessment"
        ]
        
        present_fields = sum(1 for field in required_fields if field in parsed_data)
        scores["completeness"] = present_fields / len(required_fields)
        
        # 一致性评分
        consistency_score = 1.0
        
        # 检查参数一致性
        if "strategy_parameters" in parsed_data:
            params = parsed_data["strategy_parameters"]
            
            # 检查参数范围
            if "crossover_rate" in params:
                if not (0.0 <= params["crossover_rate"] <= 1.0):
                    consistency_score -= 0.1
            
            if "mutation_rate" in params:
                if not (0.0 <= params["mutation_rate"] <= 1.0):
                    consistency_score -= 0.1
        
        # 检查置信度一致性
        if "confidence" in parsed_data:
            if not (0.0 <= parsed_data["confidence"] <= 1.0):
                consistency_score -= 0.2
        
        scores["consistency"] = max(0.0, consistency_score)
        
        # 清晰度评分（基于推理长度和结构）
        reasoning = parsed_data.get("reasoning", "")
        if len(reasoning) > 50:
            scores["clarity"] = min(1.0, len(reasoning) / 200.0)
        else:
            scores["clarity"] = 0.3
        
        # 整体评分
        scores["overall"] = (
            scores["completeness"] * 0.4 +
            scores["consistency"] * 0.4 +
            scores["clarity"] * 0.2
        )
        
        return scores
    
    def create_fallback_decision(self, context_info: Optional[Dict[str, Any]] = None) -> StrategyDecision:
        """创建备用决策"""
        # 基于上下文信息选择合适的备用策略
        if context_info:
            generation = context_info.get("generation", 0)
            
            # 早期代数使用探索策略
            if generation < 10:
                global_strategy = StrategyType.EXPLORATION
                params = StrategyParameters(
                    crossover_rate=0.7,
                    mutation_rate=0.2,
                    selection_pressure=1.5
                )
            # 中期使用平衡策略
            elif generation < 50:
                global_strategy = StrategyType.TRANSITION
                params = StrategyParameters(
                    crossover_rate=0.8,
                    mutation_rate=0.1,
                    selection_pressure=2.0
                )
            # 后期使用开发策略
            else:
                global_strategy = StrategyType.EXPLOITATION
                params = StrategyParameters(
                    crossover_rate=0.9,
                    mutation_rate=0.05,
                    selection_pressure=3.0
                )
        else:
            # 默认平衡策略
            global_strategy = StrategyType.TRANSITION
            params = StrategyParameters()
        
        return StrategyDecision(
            global_strategy=global_strategy,
            strategy_parameters=params,
            confidence=0.5,
            reasoning="使用备用策略决策",
            expected_improvement=0.05,
            risk_assessment=0.3
        )
    
    def extract_key_insights(self, reasoning: str) -> List[str]:
        """从推理文本中提取关键洞察"""
        insights = []
        
        # 关键词模式匹配
        patterns = {
            "landscape": r"景观.*?([崎岖|平滑|多模态|单模态])",
            "diversity": r"多样性.*?([高|中|低|不足|充足])",
            "convergence": r"收敛.*?([快|慢|过快|过慢])",
            "performance": r"性能.*?([提升|下降|稳定|停滞])"
        }
        
        for category, pattern in patterns.items():
            matches = re.findall(pattern, reasoning)
            if matches:
                insights.append(f"{category}: {', '.join(matches)}")
        
        return insights
