"""
Tools package for TSP analysis and visualization.

This package contains various tools for:
- Analysis: TSP path analysis and statistics
- Visualization: Enhanced TSP visualization tools
- Testing: Test utilities and validation tools
- Scheduling: Task scheduling and management tools
"""

__version__ = "1.0.0"
__author__ = "TSP Analysis Team"

# Import main tool modules for easy access
try:
    from .analysis import *
    from .visualization import *
    from .testing import *
    from .scheduling import *
except ImportError:
    # Handle import errors gracefully during development
    pass
