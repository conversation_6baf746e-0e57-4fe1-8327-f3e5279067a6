#!/usr/bin/env python3
"""
全面修复所有导入问题的脚本
"""

import os
import re

def fix_imports_in_file(file_path):
    """修复单个文件中的导入语句"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 根据文件位置确定修复规则
        relative_path = file_path.replace('\\', '/').replace('src/', '')
        
        # 专家模块的导入修复
        if relative_path.startswith('experts/'):
            # 修复utils导入
            content = re.sub(r'^from utils import', 'from ...utils import', content, flags=re.MULTILINE)
            content = re.sub(r'^import utils', 'from ...utils import utils', content, flags=re.MULTILINE)
            
        # 核心算法模块的导入修复
        elif relative_path.startswith('core/algorithms/'):
            # 修复同级模块导入
            content = re.sub(r'^import gls_operators', 'from . import gls_operators', content, flags=re.MULTILINE)
            content = re.sub(r'^import gls_evol_enhanced', 'from . import gls_evol_enhanced', content, flags=re.MULTILINE)
            content = re.sub(r'^import gls_run', 'from . import gls_run', content, flags=re.MULTILINE)
            
            # 修复utils导入
            content = re.sub(r'^from utils import', 'from ...utils import', content, flags=re.MULTILINE)
            content = re.sub(r'^import utils', 'from ...utils import utils', content, flags=re.MULTILINE)
            
        # 核心优化模块的导入修复
        elif relative_path.startswith('core/optimization/'):
            # 修复utils导入
            content = re.sub(r'^from utils import', 'from ...utils import', content, flags=re.MULTILINE)
            content = re.sub(r'^import utils', 'from ...utils import utils', content, flags=re.MULTILINE)
            
        # 核心数据模块的导入修复
        elif relative_path.startswith('core/data/'):
            # 修复utils导入
            content = re.sub(r'^from utils import', 'from ...utils import', content, flags=re.MULTILINE)
            content = re.sub(r'^import utils', 'from ...utils import utils', content, flags=re.MULTILINE)
            
        # API模块的导入修复
        elif relative_path.startswith('api/'):
            # 修复config导入
            content = re.sub(r'^from config import', 'from ..config import', content, flags=re.MULTILINE)
            content = re.sub(r'^from utils import', 'from ..utils import', content, flags=re.MULTILINE)
            
        # 知识库模块的导入修复
        elif relative_path.startswith('knowledge/'):
            # 修复utils导入
            content = re.sub(r'^from utils import', 'from ..utils import', content, flags=re.MULTILINE)
            content = re.sub(r'^import utils', 'from ..utils import utils', content, flags=re.MULTILINE)
            
        # 工具模块的导入修复
        elif relative_path.startswith('utils/'):
            # 工具模块内部的相对导入
            if '/' in relative_path[6:]:  # 子目录中的工具
                content = re.sub(r'^from utils import', 'from .. import', content, flags=re.MULTILINE)
            
        # 通用修复：移除多余的相对导入点
        content = re.sub(r'from \.\.\.\.', 'from ...', content)
        content = re.sub(r'from \.\.\.\.\.', 'from ...', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"修复导入: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"修复文件失败 {file_path}: {e}")
        return False

def fix_all_imports():
    """修复所有Python文件的导入语句"""
    
    src_directory = 'src'
    
    if not os.path.exists(src_directory):
        print(f"源代码目录 {src_directory} 不存在")
        return
    
    fixed_files = 0
    total_files = 0
    
    for root, dirs, files in os.walk(src_directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                total_files += 1
                if fix_imports_in_file(file_path):
                    fixed_files += 1
    
    print(f"导入修复完成: 修复了 {fixed_files}/{total_files} 个文件")

if __name__ == "__main__":
    print("开始修复所有导入问题...")
    fix_all_imports()
    print("修复完成！")
