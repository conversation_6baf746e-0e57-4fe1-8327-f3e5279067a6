"""
LLM策略选择模块

该模块提供基于大语言模型的智能策略选择功能，包括：
- 景观特征的自然语言转换
- 结构化的LLM交互协议
- 策略决策和参数优化
- 多层次决策系统

作者: 景观指导EA系统
版本: 1.0.0
"""

from .core.strategy_selector import StrategySelector
from .core.prompt_generator import PromptGenerator
from .core.llm_interface import LLMInterface
from .core.decision_executor import DecisionExecutor
from .models.strategy_models import (
    StrategyDecision,
    StrategyParameters,
    DecisionContext,
    LLMResponse
)
from .utils.prompt_templates import PromptTemplateManager
from .utils.response_parser import ResponseParser

__all__ = [
    'StrategySelector',
    'PromptGenerator',
    'LLMInterface',
    'DecisionExecutor',
    'StrategyDecision',
    'StrategyParameters',
    'DecisionContext',
    'LLMResponse',
    'PromptTemplateManager',
    'ResponseParser'
]

__version__ = "1.0.0"
__author__ = "景观指导EA系统"
