"""
LLM策略选择模块

该模块提供基于大语言模型的智能策略选择功能，包括：
- 景观特征的自然语言转换
- 结构化的LLM交互协议
- 策略决策和参数优化
- 多层次决策系统

作者: 景观指导EA系统
版本: 1.0.0
"""

# 核心组件
from .core.strategy_selector import StrategySelector
from .core.llm_interface import LLMInterface, LLMConfig, MockLLMInterface
from .core.prompt_generator import PromptGenerator

# 数据模型
from .models.strategy_models import (
    StrategyDecision,
    StrategyParameters,
    DecisionContext,
    LLMResponse,
    StrategyExecutionResult,
    StrategyType,
    OperatorType,
    ConfidenceLevel
)

# 工具类
from .utils.response_parser import ResponseParser

# 导出的公共接口
__all__ = [
    # 核心组件
    'StrategySelector',
    'LLMInterface',
    'LLMConfig',
    'MockLLMInterface',
    'PromptGenerator',

    # 数据模型
    'StrategyDecision',
    'StrategyParameters',
    'DecisionContext',
    'LLMResponse',
    'StrategyExecutionResult',
    'StrategyType',
    'OperatorType',
    'ConfidenceLevel',

    # 工具类
    'ResponseParser',

    # 版本信息
    '__version__',
    '__author__'
]

__version__ = "1.0.0"
__author__ = "景观指导EA系统"
