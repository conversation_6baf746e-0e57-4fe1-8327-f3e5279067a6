#!/usr/bin/env python3
"""
测试重构后的模块是否正常工作
"""

import sys
import os
import numpy as np

# 添加项目根目录到系统路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(project_root)

def test_response_parser():
    """测试统一的响应解析器"""
    print("测试ResponseParser...")
    
    try:
        try:
            from src.utils import ResponseParser
        except ImportError:
            from utils import ResponseParser
        parser = ResponseParser()
        
        # 测试JSON解析
        test_response = '''```json
        {
            "new_path": [1, 2, 3, 4, 5],
            "cur_cost": 100
        }
        ```'''
        
        result = parser.extract_path_from_response(test_response)
        print(f"  JSON解析结果: {result}")
        
        # 验证键名转换
        if "new_tour" in result and "new_path" not in result:
            print("  ✓ 键名转换成功")
        else:
            print("  ✗ 键名转换失败")
            
        print("  ✓ ResponseParser测试通过")
        return True
        
    except Exception as e:
        print(f"  ✗ ResponseParser测试失败: {e}")
        return False

def test_convert_numpy_types():
    """测试统一的NumPy类型转换函数"""
    print("测试convert_numpy_types...")
    
    try:
        try:
            from src.utils import convert_numpy_types
        except ImportError:
            from utils import convert_numpy_types
        
        # 测试各种NumPy类型
        test_data = {
            "int": np.int64(42),
            "float": np.float64(3.14),
            "array": np.array([1, 2, 3]),
            "nested": {
                "inner_int": np.int32(10),
                "inner_list": [np.float32(1.5), np.int16(20)]
            }
        }
        
        result = convert_numpy_types(test_data)
        print(f"  转换结果: {result}")
        
        # 验证类型转换
        if (isinstance(result["int"], int) and 
            isinstance(result["float"], float) and
            isinstance(result["array"], list)):
            print("  ✓ NumPy类型转换成功")
        else:
            print("  ✗ NumPy类型转换失败")
            
        print("  ✓ convert_numpy_types测试通过")
        return True
        
    except Exception as e:
        print(f"  ✗ convert_numpy_types测试失败: {e}")
        return False

def test_path_similarity_optimizer():
    """测试统一的路径相似度优化器"""
    print("测试PathSimilarityOptimizer...")
    
    try:
        from core.optimization.path_similarity_optimizer import PathSimilarityOptimizer
        
        # 创建优化器实例
        optimizer = PathSimilarityOptimizer(
            similarity_threshold=0.8,
            max_cache_size=100,
            use_jit=True
        )
        
        # 测试路径添加和相似度检查
        path1 = [1, 2, 3, 4, 5, 1]
        path2 = [1, 3, 2, 4, 5, 1]  # 稍微不同的路径
        
        optimizer.add_path(path1)
        is_similar, similar_id, similarity = optimizer.check_similarity(path2)
        
        print(f"  相似度检查结果: 相似={is_similar}, ID={similar_id}, 相似度={similarity:.4f}")
        
        # 测试统计信息
        stats = optimizer.get_statistics()
        print(f"  统计信息: {stats}")
        
        print("  ✓ PathSimilarityOptimizer测试通过")
        return True
        
    except Exception as e:
        print(f"  ✗ PathSimilarityOptimizer测试失败: {e}")
        return False

def test_idea_extractor():
    """测试重构后的IdeaExtractor"""
    print("测试IdeaExtractor...")
    
    try:
        from utils.extractors.idea_extractor import IdeaExtractor
        
        extractor = IdeaExtractor()
        
        # 测试路径提取
        test_response = '''```json
        {
            "new_path": [1, 2, 3, 4, 5],
            "cur_cost": 150
        }
        ```'''
        
        result = extractor.extract_exploration_path(test_response)
        print(f"  路径提取结果: {result}")
        
        # 验证使用了统一解析器
        if "new_tour" in result:
            print("  ✓ IdeaExtractor使用统一解析器成功")
        else:
            print("  ✗ IdeaExtractor未正确使用统一解析器")
            
        print("  ✓ IdeaExtractor测试通过")
        return True
        
    except Exception as e:
        print(f"  ✗ IdeaExtractor测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 50)
    print("EoH-main 重构测试")
    print("=" * 50)
    
    tests = [
        test_response_parser,
        test_convert_numpy_types,
        test_path_similarity_optimizer,
        test_idea_extractor
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试执行失败: {e}")
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要检查重构")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
