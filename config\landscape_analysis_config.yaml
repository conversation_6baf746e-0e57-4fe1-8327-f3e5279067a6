# 景观分析系统配置文件
# 版本: 1.0.0

# 景观分析器配置
landscape_analyzer:
  # 基础配置
  window_size: 100
  enable_incremental: true
  performance_target_ms: 100.0
  
  # 特征计算配置
  feature_calculator:
    # 崎岖度计算
    ruggedness:
      max_distance: 5
      sample_size: 50
      correlation_threshold: 0.1
    
    # 多模态性计算
    modality:
      local_search_radius: 0.1
      min_improvement_threshold: 0.001
      max_local_searches: 20
    
    # 收敛性计算
    convergence:
      fitness_window: 20
      diversity_window: 15
      convergence_threshold: 0.95
    
    # 信息内容计算
    information_content:
      symbol_alphabet_size: 10
      sequence_length: 50
      entropy_normalization: true

# 历史缓存配置
history_cache:
  max_size: 200
  retention_policy: "lru"  # lru, fifo, quality_based
  cleanup_threshold: 0.8
  max_history_size: 500
  similarity_threshold: 0.8

# 触发管理器配置
trigger_manager:
  # 自适应配置
  adaptive_enabled: true
  adaptation_rate: 0.1
  target_trigger_rate: 0.1
  
  # 触发条件配置
  triggers:
    performance_stagnation:
      enabled: true
      threshold: 0.01
      window_size: 10
      min_interval: 5
    
    diversity_drop:
      enabled: true
      threshold: 0.1
      window_size: 5
      min_interval: 3
    
    generation_interval:
      enabled: true
      threshold: 10
      window_size: 1
      min_interval: 10
    
    strategy_failure:
      enabled: true
      threshold: 0.05
      window_size: 15
      min_interval: 5

# LLM策略选择配置
llm_strategy:
  # LLM接口配置
  llm_interface:
    model_name: "gpt-4"
    api_endpoint: "https://api.openai.com/v1/chat/completions"
    max_tokens: 2000
    temperature: 0.7
    timeout_seconds: 30
    retry_attempts: 3
  
  # 提示生成配置
  prompt_generator:
    template_version: "v1.0"
    include_history: true
    max_history_length: 5
    feature_description_detail: "medium"  # low, medium, high
  
  # 响应解析配置
  response_parser:
    validation_enabled: true
    fallback_strategy: "default"
    confidence_threshold: 0.3

# 精英档案配置
elite_archive:
  max_size: 100
  diversity_weight: 0.3
  fitness_weight: 0.7
  clustering_method: "kmeans"
  update_frequency: 5
  aging_factor: 0.95

# 3D可视化配置
visualization:
  # 渲染配置
  renderer:
    canvas_width: 1200
    canvas_height: 800
    background_color: "#1a1a1a"
    camera_fov: 75
    camera_near: 0.1
    camera_far: 1000
  
  # 景观可视化
  landscape:
    grid_resolution: 50
    height_scale: 10.0
    color_scheme: "viridis"
    wireframe_enabled: false
    contour_lines: true
  
  # 种群可视化
  population:
    point_size: 3.0
    elite_point_size: 5.0
    trail_length: 10
    animation_speed: 1.0
  
  # 性能优化
  performance:
    lod_enabled: true
    frustum_culling: true
    target_fps: 30
    max_particles: 1000

# 集成框架配置
integration:
  # 专家系统集成
  expert_system:
    enable_integration: true
    communication_protocol: "json_rpc"
    message_queue_size: 100
    timeout_seconds: 10
  
  # 性能监控
  monitoring:
    enable_profiling: true
    log_level: "INFO"
    metrics_collection: true
    performance_alerts: true
  
  # 实验配置
  experiments:
    enable_ab_testing: false
    baseline_strategy: "random"
    evaluation_metrics: ["fitness_improvement", "convergence_speed", "diversity_maintenance"]

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_handler:
    enabled: true
    filename: "landscape_analysis.log"
    max_bytes: 10485760  # 10MB
    backup_count: 5
  console_handler:
    enabled: true
    colored: true

# 数据存储配置
storage:
  # 结果存储
  results:
    format: "json"  # json, pickle, hdf5
    compression: true
    backup_enabled: true
    retention_days: 30
  
  # 缓存存储
  cache:
    backend: "memory"  # memory, redis, file
    persistence: false
    compression: true

# 安全配置
security:
  # API安全
  api_security:
    enable_authentication: false
    api_key_required: false
    rate_limiting: true
    max_requests_per_minute: 60
  
  # 数据安全
  data_security:
    encrypt_sensitive_data: false
    anonymize_logs: true
    secure_communication: true

# 开发配置
development:
  debug_mode: false
  profiling_enabled: false
  test_mode: false
  mock_llm_responses: false
  
  # 测试配置
  testing:
    unit_test_coverage_threshold: 0.9
    integration_test_enabled: true
    performance_test_enabled: true
    
  # 文档配置
  documentation:
    auto_generate: true
    include_examples: true
    api_documentation: true
