"""
Analysis tools for TSP instances and solutions.

This module provides tools for:
- TSP path analysis and normalization
- Solution plotting and visualization
- Statistical analysis of TSP instances
"""

# Import analysis functions for easy access
try:
    from .analyze_tsp_paths import normalize_tsp_path, find_duplicate_paths, print_duplicate_info
    from .plot_all_instances import plot_all_instances
    from .plot_solution_paths import plot_tsp_path, process_all_solutions, process_specific_solution
    from .plot_specific_solution import plot_tsp_path as plot_specific_tsp_path
except ImportError as e:
    print(f"Warning: Could not import some analysis tools: {e}")
    pass
