2025-07-31 16:26:03,930 - __main__ - INFO - 生成TSP问题实例...
2025-07-31 16:26:03,931 - __main__ - INFO - 初始化景观分析器...
2025-07-31 16:26:03,931 - src.landscape_analysis.core.history_cache - INFO - 历史缓存初始化完成，最大大小: 200
2025-07-31 16:26:03,931 - src.landscape_analysis.core.trigger_manager - INFO - 触发管理器初始化完成
2025-07-31 16:26:03,931 - src.landscape_analysis.core.landscape_analyzer - INFO - 景观分析器初始化完成，窗口大小: 100
2025-07-31 16:26:03,931 - __main__ - INFO - 初始化策略选择器...
2025-07-31 16:26:03,966 - src.llm_strategy.core.prompt_generator - INFO - 提示生成器初始化完成，模板版本: v1.0
2025-07-31 16:26:03,966 - src.llm_strategy.core.llm_interface - INFO - LLM接口初始化完成，模型: gpt-4
2025-07-31 16:26:03,966 - src.llm_strategy.utils.response_parser - INFO - 响应解析器初始化完成
2025-07-31 16:26:03,966 - src.llm_strategy.core.strategy_selector - INFO - 策略选择器初始化完成
2025-07-31 16:26:03,966 - __main__ - INFO - 开始集成演示...
2025-07-31 16:26:03,967 - __main__ - INFO - 开始集成进化过程 - 问题规模: 15, 种群大小: 40, 代数: 30
2025-07-31 16:26:03,968 - src.landscape_analysis.core.landscape_analyzer - INFO - 开始景观分析 - 代数: 0
2025-07-31 16:26:03,975 - src.landscape_analysis.core.landscape_analyzer - INFO - 景观分析完成 - 耗时: 7.00ms
2025-07-31 16:26:04,488 - src.llm_strategy.core.strategy_selector - INFO - 策略选择完成 - 策略: exploration, 置信度: 0.750, 耗时: 0.51秒
2025-07-31 16:26:04,496 - __main__ - INFO - 代数 0: 最佳适应度=0.001695, 平均适应度=0.001395, 多样性=0.936
2025-07-31 16:26:04,496 - __main__ - INFO -   景观特征: 崎岖度=0.867, 多模态性=1.000, 收敛度=0.000
2025-07-31 16:26:04,496 - __main__ - INFO -   策略: exploration, 置信度=0.750, 改善=0.000559
2025-07-31 16:26:04,498 - __main__ - WARNING - 代数 1: 景观分析失败，使用默认策略
2025-07-31 16:26:04,501 - __main__ - WARNING - 代数 2: 景观分析失败，使用默认策略
2025-07-31 16:26:04,505 - __main__ - WARNING - 代数 3: 景观分析失败，使用默认策略
2025-07-31 16:26:04,510 - src.landscape_analysis.core.trigger_manager - INFO - 触发景观分析 - 代数: 4, 条件: ['performance_stagnation']
2025-07-31 16:26:04,510 - src.landscape_analysis.core.landscape_analyzer - INFO - 开始景观分析 - 代数: 4
2025-07-31 16:26:04,510 - src.landscape_analysis.core.landscape_analyzer - INFO - 使用缓存的分析结果
2025-07-31 16:26:04,515 - __main__ - INFO - 代数 4: 最佳适应度=0.002254, 平均适应度=0.001579, 多样性=0.906
2025-07-31 16:26:04,516 - __main__ - INFO -   景观特征: 崎岖度=0.867, 多模态性=1.000, 收敛度=0.000
2025-07-31 16:26:04,516 - __main__ - INFO -   策略: exploration, 置信度=0.750, 改善=0.000243
2025-07-31 16:26:04,518 - __main__ - WARNING - 代数 5: 景观分析失败，使用默认策略
2025-07-31 16:26:04,521 - __main__ - WARNING - 代数 6: 景观分析失败，使用默认策略
2025-07-31 16:26:04,527 - __main__ - WARNING - 代数 7: 景观分析失败，使用默认策略
2025-07-31 16:26:04,531 - __main__ - WARNING - 代数 8: 景观分析失败，使用默认策略
2025-07-31 16:26:04,535 - src.landscape_analysis.core.trigger_manager - INFO - 触发景观分析 - 代数: 9, 条件: ['performance_stagnation', 'generation_interval']
2025-07-31 16:26:04,535 - src.landscape_analysis.core.landscape_analyzer - INFO - 开始景观分析 - 代数: 9
2025-07-31 16:26:04,536 - src.landscape_analysis.core.landscape_analyzer - INFO - 使用缓存的分析结果
2025-07-31 16:26:04,542 - __main__ - INFO - 代数 9: 最佳适应度=0.002497, 平均适应度=0.001859, 多样性=0.893
2025-07-31 16:26:04,544 - __main__ - INFO -   景观特征: 崎岖度=0.867, 多模态性=1.000, 收敛度=0.000
2025-07-31 16:26:04,544 - __main__ - INFO -   策略: exploration, 置信度=0.750, 改善=0.000179
2025-07-31 16:26:04,548 - __main__ - WARNING - 代数 10: 景观分析失败，使用默认策略
2025-07-31 16:26:04,553 - __main__ - WARNING - 代数 11: 景观分析失败，使用默认策略
2025-07-31 16:26:04,556 - __main__ - WARNING - 代数 12: 景观分析失败，使用默认策略
2025-07-31 16:26:04,561 - __main__ - WARNING - 代数 13: 景观分析失败，使用默认策略
2025-07-31 16:26:04,565 - src.landscape_analysis.core.trigger_manager - INFO - 触发景观分析 - 代数: 14, 条件: ['performance_stagnation']
2025-07-31 16:26:04,565 - src.landscape_analysis.core.landscape_analyzer - INFO - 开始景观分析 - 代数: 14
2025-07-31 16:26:04,566 - src.landscape_analysis.core.landscape_analyzer - INFO - 使用缓存的分析结果
2025-07-31 16:26:04,571 - __main__ - INFO - 代数 14: 最佳适应度=0.002676, 平均适应度=0.001841, 多样性=0.907
2025-07-31 16:26:04,571 - __main__ - INFO -   景观特征: 崎岖度=0.867, 多模态性=1.000, 收敛度=0.000
2025-07-31 16:26:04,571 - __main__ - INFO -   策略: exploration, 置信度=0.750, 改善=0.000037
2025-07-31 16:26:04,574 - __main__ - WARNING - 代数 15: 景观分析失败，使用默认策略
2025-07-31 16:26:04,579 - __main__ - WARNING - 代数 16: 景观分析失败，使用默认策略
2025-07-31 16:26:04,582 - __main__ - WARNING - 代数 17: 景观分析失败，使用默认策略
2025-07-31 16:26:04,586 - __main__ - WARNING - 代数 18: 景观分析失败，使用默认策略
2025-07-31 16:26:04,589 - src.landscape_analysis.core.trigger_manager - INFO - 触发景观分析 - 代数: 19, 条件: ['performance_stagnation', 'generation_interval']
2025-07-31 16:26:04,589 - src.landscape_analysis.core.landscape_analyzer - INFO - 开始景观分析 - 代数: 19
2025-07-31 16:26:04,590 - src.landscape_analysis.core.landscape_analyzer - INFO - 使用缓存的分析结果
2025-07-31 16:26:04,598 - __main__ - INFO - 代数 19: 最佳适应度=0.002713, 平均适应度=0.002183, 多样性=0.879
2025-07-31 16:26:04,598 - __main__ - INFO -   景观特征: 崎岖度=0.867, 多模态性=1.000, 收敛度=0.000
2025-07-31 16:26:04,599 - __main__ - INFO -   策略: exploration, 置信度=0.750, 改善=0.000000
2025-07-31 16:26:04,601 - __main__ - WARNING - 代数 20: 景观分析失败，使用默认策略
2025-07-31 16:26:04,604 - __main__ - WARNING - 代数 21: 景观分析失败，使用默认策略
2025-07-31 16:26:04,609 - __main__ - WARNING - 代数 22: 景观分析失败，使用默认策略
2025-07-31 16:26:04,614 - __main__ - WARNING - 代数 23: 景观分析失败，使用默认策略
2025-07-31 16:26:04,619 - src.landscape_analysis.core.trigger_manager - INFO - 触发景观分析 - 代数: 24, 条件: ['performance_stagnation']
2025-07-31 16:26:04,619 - src.landscape_analysis.core.landscape_analyzer - INFO - 开始景观分析 - 代数: 24
2025-07-31 16:26:04,619 - src.landscape_analysis.core.landscape_analyzer - INFO - 使用缓存的分析结果
2025-07-31 16:26:04,626 - __main__ - INFO - 代数 24: 最佳适应度=0.002713, 平均适应度=0.002108, 多样性=0.824
2025-07-31 16:26:04,627 - __main__ - INFO -   景观特征: 崎岖度=0.867, 多模态性=1.000, 收敛度=0.000
2025-07-31 16:26:04,627 - __main__ - INFO -   策略: exploration, 置信度=0.750, 改善=0.000000
2025-07-31 16:26:04,629 - __main__ - WARNING - 代数 25: 景观分析失败，使用默认策略
2025-07-31 16:26:04,632 - __main__ - WARNING - 代数 26: 景观分析失败，使用默认策略
2025-07-31 16:26:04,636 - __main__ - WARNING - 代数 27: 景观分析失败，使用默认策略
2025-07-31 16:26:04,639 - __main__ - WARNING - 代数 28: 景观分析失败，使用默认策略
2025-07-31 16:26:04,643 - src.landscape_analysis.core.trigger_manager - INFO - 触发景观分析 - 代数: 29, 条件: ['performance_stagnation', 'generation_interval']
2025-07-31 16:26:04,644 - src.landscape_analysis.core.landscape_analyzer - INFO - 开始景观分析 - 代数: 29
2025-07-31 16:26:04,644 - src.landscape_analysis.utils.sliding_window - INFO - 自适应调整窗口大小: 100 -> 120
2025-07-31 16:26:04,644 - src.landscape_analysis.core.landscape_analyzer - INFO - 使用缓存的分析结果
2025-07-31 16:26:04,652 - __main__ - INFO - 代数 29: 最佳适应度=0.002713, 平均适应度=0.002127, 多样性=0.770
2025-07-31 16:26:04,652 - __main__ - INFO -   景观特征: 崎岖度=0.867, 多模态性=1.000, 收敛度=0.000
2025-07-31 16:26:04,652 - __main__ - INFO -   策略: exploration, 置信度=0.750, 改善=0.000000
2025-07-31 16:26:04,655 - __main__ - INFO - 集成演示完成
