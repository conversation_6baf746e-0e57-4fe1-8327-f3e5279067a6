import numpy as np
import matplotlib.pyplot as plt
import os
import glob
import re

def read_tsp_file(file_path):
    """
    读取TSP文件中的城市坐标
    
    :param file_path: TSP文件路径
    :return: 城市坐标的numpy数组，形状为(n, 2)
    """
    cities = []
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    # 直接分割行，获取坐标
                    parts = line.split()
                    if len(parts) >= 2:
                        x, y = float(parts[0]), float(parts[1])
                        cities.append([x, y])
                except (ValueError, IndexError):
                    continue
    return np.array(cities)

def read_solution_file(file_path):
    """
    读取solution文件中的路径和成本
    
    :param file_path: solution文件路径
    :return: 列表，每个元素是一个元组(cost, path)
    """
    solutions = []
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) > 1:
                    try:
                        cost = int(parts[0])
                        path = [int(node) for node in parts[1:]]
                        solutions.append((cost, path))
                    except ValueError:
                        continue
    return solutions

def plot_tsp_path(cities, path, instance_name, cost, solution_index, output_folder):
    """
    绘制TSP路径
    
    :param cities: 城市的坐标，形状为(n, 2)的数组
    :param path: TSP路径，列表中元素为城市的索引
    :param instance_name: 实例名称
    :param cost: 路径成本
    :param solution_index: 解决方案索引
    :param output_folder: 输出文件夹路径
    """
    # 确保城市和路径的有效性
    if not isinstance(cities, np.ndarray) or cities.shape[1] != 2:
        raise ValueError("cities应该是一个形状为(n, 2)的numpy数组")
    if not isinstance(path, list) or not all(isinstance(i, int) for i in path):
        raise ValueError("path应该是一个包含城市索引的列表")
    
    # 初始化绘图
    plt.figure(figsize=(10, 6))
    
    # 绘制城市
    plt.scatter(cities[:, 0], cities[:, 1], color='red')
    
    # 通过路径连接城市
    path_cities = np.array([cities[i] for i in path if i < len(cities)])
    
    if len(path_cities) > 0:
        plt.plot(path_cities[:, 0], path_cities[:, 1], marker='o', color='blue')
        
        # 使路径形成闭环（如果路径的第一个和最后一个节点不同）
        if path[0] != path[-1] and len(path_cities) > 1:
            plt.plot([path_cities[-1][0], path_cities[0][0]], 
                     [path_cities[-1][1], path_cities[0][1]], color='blue', marker='o')
    
    # 标注城市
    for i, city in enumerate(cities):
        plt.text(city[0], city[1], str(i), fontsize=12, ha='right')
    
    plt.title(f"{instance_name} - Solution {solution_index+1} - Cost: {cost}")
    plt.xlabel("X")
    plt.ylabel("Y")
    plt.grid()
    
    # 创建实例专用文件夹
    instance_folder = os.path.join(output_folder, instance_name)
    if not os.path.exists(instance_folder):
        os.makedirs(instance_folder)
    
    # 保存图像
    plt.savefig(os.path.join(instance_folder, f"{instance_name}_{solution_index+1}.png"))
    plt.close()

if __name__ == "__main__":
    # 设置输入和输出文件夹
    solution_file = "c:\\Users\\<USER>\\Desktop\\EoH-main - idea - 0312-feedback\\EoH-main\\results\\solutions\\composite13_66_20250323_144130.solution"
    tsp_file = "c:\\Users\\<USER>\\Desktop\\EoH-main - idea - 0312-feedback\\benchmark_MMTSP\\composite13_66.tsp"
    output_folder = "c:\\Users\\<USER>\\Desktop\\EoH-main - idea - 0312-feedback\\results\\solution_plots"
    
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 提取实例名称
    instance_name = "composite13_66_20250323_144130"
    print(f"处理实例: {instance_name}")
    
    if os.path.exists(tsp_file):
        # 读取城市坐标
        cities = read_tsp_file(tsp_file)
        
        # 读取解决方案
        solutions = read_solution_file(solution_file)
        
        # 为每个解决方案绘制路径
        for i, (cost, path) in enumerate(solutions):
            plot_tsp_path(cities, path, instance_name, cost, i, output_folder)
            print(f"  - 已绘制解决方案 {i+1}/{len(solutions)}")
    else:
        print(f"警告: 找不到对应的TSP文件 {tsp_file}")