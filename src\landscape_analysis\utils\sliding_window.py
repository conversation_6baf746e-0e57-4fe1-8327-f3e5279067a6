"""
滑动窗口管理模块

实现高效的滑动窗口数据管理，支持：
- 固定大小的循环缓冲区
- 自适应窗口大小调整
- 多种采样策略
- 内存优化的数据存储
"""

from collections import deque
from typing import Deque, List, Any, Tuple, Dict, Optional, Callable
import numpy as np
import logging
import time
from ..models.data_structures import Individual, Fitness, Generation


class SlidingWindowManager:
    """
    滑动窗口管理器
    
    使用循环缓冲区实现固定内存占用的滑动窗口。
    """
    
    def __init__(self, max_size: int = 100, adaptive_sizing: bool = True):
        """
        初始化滑动窗口管理器
        
        Args:
            max_size: 最大窗口大小
            adaptive_sizing: 是否启用自适应大小调整
        """
        self.max_size = max(1, max_size)
        self.adaptive_sizing = adaptive_sizing
        
        # 使用deque实现循环缓冲区
        self.fitness_window: Deque[Fitness] = deque(maxlen=max_size)
        self.solution_window: Deque[Individual] = deque(maxlen=max_size)
        self.generation_window: Deque[Generation] = deque(maxlen=max_size)
        self.timestamp_window: Deque[float] = deque(maxlen=max_size)
        
        # 统计信息
        self.total_samples_added = 0
        self.last_cleanup_time = time.time()
        
        self.logger = logging.getLogger(__name__)
    
    def add_sample(self, solution: Individual, fitness: Fitness, generation: Generation) -> None:
        """
        添加单个样本到滑动窗口
        
        Args:
            solution: 解个体
            fitness: 适应度值
            generation: 代数
        """
        current_time = time.time()
        
        self.solution_window.append(solution)
        self.fitness_window.append(fitness)
        self.generation_window.append(generation)
        self.timestamp_window.append(current_time)
        
        self.total_samples_added += 1
        
        # 自适应调整窗口大小
        if self.adaptive_sizing and self.total_samples_added % 50 == 0:
            self._adaptive_resize()
    
    def add_samples(self, solutions: List[Individual], fitnesses: List[Fitness], 
                   generation: Generation) -> None:
        """
        批量添加样本到滑动窗口
        
        Args:
            solutions: 解个体列表
            fitnesses: 适应度值列表
            generation: 代数
        """
        if len(solutions) != len(fitnesses):
            self.logger.error("解个体数量与适应度数量不匹配")
            return
        
        for solution, fitness in zip(solutions, fitnesses):
            self.add_sample(solution, fitness, generation)
    
    def get_recent_samples(self, n: Optional[int] = None) -> Tuple[List[Individual], np.ndarray]:
        """
        获取最近的n个样本
        
        Args:
            n: 样本数量，如果为None则返回所有样本
            
        Returns:
            (解个体列表, 适应度数组)
        """
        if n is None:
            n = len(self.solution_window)
        
        n = min(n, len(self.solution_window))
        
        if n <= 0:
            return [], np.array([])
        
        recent_solutions = list(self.solution_window)[-n:]
        recent_fitnesses = np.array(list(self.fitness_window)[-n:])
        
        return recent_solutions, recent_fitnesses
    
    def get_samples_by_generation(self, start_gen: Generation, 
                                 end_gen: Optional[Generation] = None) -> Tuple[List[Individual], np.ndarray]:
        """
        根据代数范围获取样本
        
        Args:
            start_gen: 起始代数
            end_gen: 结束代数，如果为None则到最新代数
            
        Returns:
            (解个体列表, 适应度数组)
        """
        if end_gen is None:
            end_gen = max(self.generation_window) if self.generation_window else start_gen
        
        solutions = []
        fitnesses = []
        
        for i, gen in enumerate(self.generation_window):
            if start_gen <= gen <= end_gen:
                solutions.append(self.solution_window[i])
                fitnesses.append(self.fitness_window[i])
        
        return solutions, np.array(fitnesses)
    
    def get_window_statistics(self) -> Dict[str, Any]:
        """
        获取窗口统计信息
        
        Returns:
            统计信息字典
        """
        if len(self.fitness_window) == 0:
            return {
                "size": 0,
                "fitness_stats": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0},
                "generation_range": (0, 0),
                "time_span": 0.0,
                "memory_usage": 0
            }
        
        fitnesses = np.array(self.fitness_window)
        generations = list(self.generation_window)
        timestamps = list(self.timestamp_window)
        
        return {
            "size": len(self.fitness_window),
            "fitness_stats": {
                "mean": float(np.mean(fitnesses)),
                "std": float(np.std(fitnesses)),
                "min": float(np.min(fitnesses)),
                "max": float(np.max(fitnesses))
            },
            "generation_range": (min(generations), max(generations)),
            "time_span": max(timestamps) - min(timestamps),
            "memory_usage": self._estimate_memory_usage()
        }
    
    def sample_diverse_solutions(self, k: int, diversity_func: Callable[[Individual, Individual], float]) -> List[Individual]:
        """
        采样多样化的解
        
        Args:
            k: 采样数量
            diversity_func: 多样性计算函数
            
        Returns:
            多样化的解列表
        """
        if len(self.solution_window) <= k:
            return list(self.solution_window)
        
        solutions = list(self.solution_window)
        selected = [solutions[0]]  # 选择第一个解作为起始点
        
        for _ in range(k - 1):
            best_candidate = None
            best_min_distance = -1
            
            for candidate in solutions:
                if candidate in selected:
                    continue
                
                # 计算候选解到已选解的最小距离
                min_distance = min(diversity_func(candidate, selected_sol) 
                                 for selected_sol in selected)
                
                if min_distance > best_min_distance:
                    best_min_distance = min_distance
                    best_candidate = candidate
            
            if best_candidate is not None:
                selected.append(best_candidate)
        
        return selected
    
    def clear(self) -> None:
        """清空窗口"""
        self.fitness_window.clear()
        self.solution_window.clear()
        self.generation_window.clear()
        self.timestamp_window.clear()
        self.total_samples_added = 0
        self.logger.info("滑动窗口已清空")
    
    def _adaptive_resize(self) -> None:
        """自适应调整窗口大小"""
        if not self.adaptive_sizing:
            return
        
        # 基于数据变化率调整窗口大小
        if len(self.fitness_window) < 10:
            return
        
        recent_fitnesses = np.array(list(self.fitness_window)[-20:])
        fitness_cv = np.std(recent_fitnesses) / (np.mean(recent_fitnesses) + 1e-10)
        
        # 如果数据变化剧烈，增大窗口；如果稳定，减小窗口
        if fitness_cv > 0.2:  # 高变异性
            new_size = min(self.max_size * 1.2, 200)
        elif fitness_cv < 0.05:  # 低变异性
            new_size = max(self.max_size * 0.8, 20)
        else:
            return  # 保持当前大小
        
        new_size = int(new_size)
        if new_size != self.max_size:
            self.logger.info(f"自适应调整窗口大小: {self.max_size} -> {new_size}")
            self._resize_window(new_size)
    
    def _resize_window(self, new_size: int) -> None:
        """调整窗口大小"""
        # 保存当前数据
        current_solutions = list(self.solution_window)
        current_fitnesses = list(self.fitness_window)
        current_generations = list(self.generation_window)
        current_timestamps = list(self.timestamp_window)
        
        # 更新最大大小
        self.max_size = new_size
        
        # 重新创建deque
        self.solution_window = deque(maxlen=new_size)
        self.fitness_window = deque(maxlen=new_size)
        self.generation_window = deque(maxlen=new_size)
        self.timestamp_window = deque(maxlen=new_size)
        
        # 恢复数据（自动截断到新大小）
        for sol, fit, gen, ts in zip(current_solutions, current_fitnesses, 
                                   current_generations, current_timestamps):
            self.solution_window.append(sol)
            self.fitness_window.append(fit)
            self.generation_window.append(gen)
            self.timestamp_window.append(ts)
    
    def _estimate_memory_usage(self) -> int:
        """估算内存使用量（字节）"""
        # 简化的内存估算
        base_size = 64  # 基础对象大小
        solution_size = 100  # 假设每个解占用100字节
        fitness_size = 8  # float64
        generation_size = 8  # int64
        timestamp_size = 8  # float64
        
        total_size = (len(self.solution_window) * 
                     (solution_size + fitness_size + generation_size + timestamp_size) + 
                     base_size)
        
        return total_size
    
    def __len__(self) -> int:
        """返回当前窗口大小"""
        return len(self.solution_window)
    
    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_window_statistics()
        return (f"SlidingWindow(size={stats['size']}/{self.max_size}, "
                f"generations={stats['generation_range']}, "
                f"fitness_range=[{stats['fitness_stats']['min']:.3f}, "
                f"{stats['fitness_stats']['max']:.3f}])")


class AdaptiveSamplingWindow(SlidingWindowManager):
    """
    自适应采样窗口
    
    根据景观特征动态调整采样策略。
    """
    
    def __init__(self, max_size: int = 100, sampling_strategies: List[str] = None):
        """
        初始化自适应采样窗口
        
        Args:
            max_size: 最大窗口大小
            sampling_strategies: 采样策略列表
        """
        super().__init__(max_size, adaptive_sizing=True)
        
        self.sampling_strategies = sampling_strategies or [
            "random", "fitness_based", "diversity_based", "recent_bias"
        ]
        self.current_strategy = "random"
        self.strategy_performance = {strategy: 0.0 for strategy in self.sampling_strategies}
    
    def adaptive_sample(self, k: int, landscape_features: Dict[str, float] = None) -> Tuple[List[Individual], np.ndarray]:
        """
        自适应采样
        
        Args:
            k: 采样数量
            landscape_features: 景观特征
            
        Returns:
            (采样的解列表, 对应的适应度数组)
        """
        if len(self.solution_window) <= k:
            return self.get_recent_samples()
        
        # 根据景观特征选择采样策略
        if landscape_features:
            self._select_sampling_strategy(landscape_features)
        
        # 执行采样
        if self.current_strategy == "random":
            return self._random_sample(k)
        elif self.current_strategy == "fitness_based":
            return self._fitness_based_sample(k)
        elif self.current_strategy == "diversity_based":
            return self._diversity_based_sample(k)
        elif self.current_strategy == "recent_bias":
            return self._recent_bias_sample(k)
        else:
            return self._random_sample(k)
    
    def _select_sampling_strategy(self, landscape_features: Dict[str, float]) -> None:
        """根据景观特征选择采样策略"""
        ruggedness = landscape_features.get('ruggedness', 0.5)
        modality = landscape_features.get('modality', 0.5)
        convergence = landscape_features.get('convergence', 0.5)
        
        # 策略选择逻辑
        if ruggedness > 0.7:  # 高崎岖度
            self.current_strategy = "diversity_based"
        elif convergence > 0.8:  # 高收敛度
            self.current_strategy = "fitness_based"
        elif modality > 0.6:  # 高多模态性
            self.current_strategy = "random"
        else:
            self.current_strategy = "recent_bias"
    
    def _random_sample(self, k: int) -> Tuple[List[Individual], np.ndarray]:
        """随机采样"""
        indices = np.random.choice(len(self.solution_window), k, replace=False)
        solutions = [self.solution_window[i] for i in indices]
        fitnesses = np.array([self.fitness_window[i] for i in indices])
        return solutions, fitnesses
    
    def _fitness_based_sample(self, k: int) -> Tuple[List[Individual], np.ndarray]:
        """基于适应度的采样（偏向高适应度）"""
        fitnesses = np.array(self.fitness_window)
        # 使用softmax进行概率采样
        exp_fitnesses = np.exp(fitnesses - np.max(fitnesses))
        probabilities = exp_fitnesses / np.sum(exp_fitnesses)
        
        indices = np.random.choice(len(self.solution_window), k, replace=False, p=probabilities)
        solutions = [self.solution_window[i] for i in indices]
        sampled_fitnesses = np.array([self.fitness_window[i] for i in indices])
        return solutions, sampled_fitnesses
    
    def _diversity_based_sample(self, k: int) -> Tuple[List[Individual], np.ndarray]:
        """基于多样性的采样"""
        # 简化实现：选择适应度分布较为均匀的样本
        fitnesses = np.array(self.fitness_window)
        sorted_indices = np.argsort(fitnesses)
        
        # 均匀选择
        step = len(sorted_indices) // k
        selected_indices = sorted_indices[::step][:k]
        
        solutions = [self.solution_window[i] for i in selected_indices]
        sampled_fitnesses = np.array([self.fitness_window[i] for i in selected_indices])
        return solutions, sampled_fitnesses
    
    def _recent_bias_sample(self, k: int) -> Tuple[List[Individual], np.ndarray]:
        """偏向最近样本的采样"""
        # 给最近的样本更高的权重
        n = len(self.solution_window)
        weights = np.exp(np.arange(n) / n)  # 指数权重
        probabilities = weights / np.sum(weights)
        
        indices = np.random.choice(n, k, replace=False, p=probabilities)
        solutions = [self.solution_window[i] for i in indices]
        fitnesses = np.array([self.fitness_window[i] for i in indices])
        return solutions, fitnesses
