"""
景观分析系统演示示例

展示如何使用景观分析系统进行适应度景观分析和策略选择。
"""

import sys
import os
import numpy as np
import logging
from typing import List, Any
import time

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.landscape_analysis import (
    LandscapeAnalyzer, 
    PopulationState,
    LandscapeFeatures
)
from src.landscape_analysis.models.data_structures import SearchPhase


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('landscape_analysis_demo.log')
        ]
    )


def generate_tsp_individual(size: int) -> List[int]:
    """生成TSP个体（随机排列）"""
    individual = list(range(size))
    np.random.shuffle(individual)
    return individual


def calculate_tsp_fitness(individual: List[int], distance_matrix: np.ndarray) -> float:
    """计算TSP适应度（路径长度的倒数）"""
    total_distance = 0.0
    n = len(individual)
    
    for i in range(n):
        from_city = individual[i]
        to_city = individual[(i + 1) % n]
        total_distance += distance_matrix[from_city][to_city]
    
    # 返回适应度（距离越短，适应度越高）
    return 1.0 / (1.0 + total_distance)


def generate_distance_matrix(size: int) -> np.ndarray:
    """生成随机距离矩阵"""
    np.random.seed(42)  # 固定随机种子以便重现
    matrix = np.random.rand(size, size) * 100
    
    # 确保对称性和对角线为0
    matrix = (matrix + matrix.T) / 2
    np.fill_diagonal(matrix, 0)
    
    return matrix


def tsp_distance_function(individual1: List[int], individual2: List[int]) -> float:
    """计算两个TSP个体之间的距离（汉明距离）"""
    if len(individual1) != len(individual2):
        return 1.0
    
    differences = sum(1 for a, b in zip(individual1, individual2) if a != b)
    return differences / len(individual1)


def simulate_evolution_process(analyzer: LandscapeAnalyzer, 
                             distance_matrix: np.ndarray,
                             num_generations: int = 50,
                             population_size: int = 30) -> None:
    """模拟进化过程"""
    logger = logging.getLogger(__name__)
    problem_size = distance_matrix.shape[0]
    
    logger.info(f"开始模拟进化过程 - 问题规模: {problem_size}, 种群大小: {population_size}, 代数: {num_generations}")
    
    # 初始化种群
    population = [generate_tsp_individual(problem_size) for _ in range(population_size)]
    
    analysis_results = []
    
    for generation in range(num_generations):
        # 计算适应度
        fitnesses = np.array([
            calculate_tsp_fitness(individual, distance_matrix) 
            for individual in population
        ])
        
        # 计算多样性（简化版本）
        diversity = calculate_population_diversity(population)
        
        # 创建种群状态
        population_state = PopulationState(
            individuals=population.copy(),
            fitnesses=fitnesses,
            generation=generation,
            diversity=diversity
        )
        
        # 执行景观分析
        analysis_result = analyzer.analyze(population_state)
        
        if analysis_result:
            analysis_results.append(analysis_result)
            
            # 打印分析结果
            features = analysis_result.landscape_features
            logger.info(f"代数 {generation}: 崎岖度={features.ruggedness:.3f}, "
                       f"多模态性={features.modality:.3f}, 收敛度={features.convergence:.3f}, "
                       f"搜索阶段={features.search_phase.value}")
        
        # 模拟进化操作（简化版本）
        population = simulate_evolution_operators(population, fitnesses, distance_matrix)
        
        # 每10代输出一次详细信息
        if generation % 10 == 0:
            best_fitness = np.max(fitnesses)
            mean_fitness = np.mean(fitnesses)
            logger.info(f"代数 {generation}: 最佳适应度={best_fitness:.6f}, "
                       f"平均适应度={mean_fitness:.6f}, 多样性={diversity:.3f}")
    
    # 输出分析摘要
    print_analysis_summary(analysis_results)


def calculate_population_diversity(population: List[List[int]]) -> float:
    """计算种群多样性"""
    if len(population) < 2:
        return 0.0
    
    total_distance = 0.0
    pair_count = 0
    
    for i in range(len(population)):
        for j in range(i + 1, len(population)):
            distance = tsp_distance_function(population[i], population[j])
            total_distance += distance
            pair_count += 1
    
    return total_distance / pair_count if pair_count > 0 else 0.0


def simulate_evolution_operators(population: List[List[int]], 
                               fitnesses: np.ndarray,
                               distance_matrix: np.ndarray) -> List[List[int]]:
    """模拟进化算子"""
    new_population = []
    population_size = len(population)
    
    # 精英保留
    elite_count = max(1, population_size // 10)
    elite_indices = np.argsort(fitnesses)[-elite_count:]
    for idx in elite_indices:
        new_population.append(population[idx].copy())
    
    # 生成剩余个体
    while len(new_population) < population_size:
        # 选择父代（轮盘赌选择）
        parent1_idx = roulette_wheel_selection(fitnesses)
        parent2_idx = roulette_wheel_selection(fitnesses)
        
        # 交叉
        child = order_crossover(population[parent1_idx], population[parent2_idx])
        
        # 变异
        if np.random.random() < 0.1:  # 10%变异率
            child = swap_mutation(child)
        
        new_population.append(child)
    
    return new_population[:population_size]


def roulette_wheel_selection(fitnesses: np.ndarray) -> int:
    """轮盘赌选择"""
    total_fitness = np.sum(fitnesses)
    if total_fitness <= 0:
        return np.random.randint(len(fitnesses))
    
    selection_point = np.random.random() * total_fitness
    cumulative_fitness = 0.0
    
    for i, fitness in enumerate(fitnesses):
        cumulative_fitness += fitness
        if cumulative_fitness >= selection_point:
            return i
    
    return len(fitnesses) - 1


def order_crossover(parent1: List[int], parent2: List[int]) -> List[int]:
    """顺序交叉（OX）"""
    size = len(parent1)
    start = np.random.randint(0, size)
    end = np.random.randint(start + 1, size + 1)
    
    child = [-1] * size
    child[start:end] = parent1[start:end]
    
    pointer = end % size
    for city in parent2[end:] + parent2[:end]:
        if city not in child:
            child[pointer] = city
            pointer = (pointer + 1) % size
    
    return child


def swap_mutation(individual: List[int]) -> List[int]:
    """交换变异"""
    mutated = individual.copy()
    size = len(mutated)
    
    if size > 1:
        i, j = np.random.choice(size, 2, replace=False)
        mutated[i], mutated[j] = mutated[j], mutated[i]
    
    return mutated


def print_analysis_summary(analysis_results: List[Any]) -> None:
    """打印分析摘要"""
    if not analysis_results:
        print("没有分析结果")
        return
    
    print("\n" + "="*60)
    print("景观分析摘要")
    print("="*60)
    
    # 统计搜索阶段分布
    phase_counts = {}
    ruggedness_values = []
    modality_values = []
    convergence_values = []
    
    for result in analysis_results:
        features = result.landscape_features
        phase = features.search_phase.value
        phase_counts[phase] = phase_counts.get(phase, 0) + 1
        
        ruggedness_values.append(features.ruggedness)
        modality_values.append(features.modality)
        convergence_values.append(features.convergence)
    
    print(f"总分析次数: {len(analysis_results)}")
    print(f"搜索阶段分布: {phase_counts}")
    
    print(f"\n特征统计:")
    print(f"崎岖度 - 均值: {np.mean(ruggedness_values):.3f}, 标准差: {np.std(ruggedness_values):.3f}")
    print(f"多模态性 - 均值: {np.mean(modality_values):.3f}, 标准差: {np.std(modality_values):.3f}")
    print(f"收敛度 - 均值: {np.mean(convergence_values):.3f}, 标准差: {np.std(convergence_values):.3f}")
    
    # 分析器性能统计
    if hasattr(analysis_results[0], 'analysis_time'):
        analysis_times = [result.analysis_time * 1000 for result in analysis_results]  # 转换为毫秒
        print(f"\n性能统计:")
        print(f"分析时间 - 均值: {np.mean(analysis_times):.2f}ms, 最大: {np.max(analysis_times):.2f}ms")


def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("景观分析系统演示")
    print("="*50)
    
    # 配置参数
    problem_size = 20  # TSP城市数量
    population_size = 30
    num_generations = 50
    
    try:
        # 生成TSP问题实例
        logger.info("生成TSP问题实例...")
        distance_matrix = generate_distance_matrix(problem_size)
        
        # 初始化景观分析器
        logger.info("初始化景观分析器...")
        analyzer = LandscapeAnalyzer(
            window_size=100,
            distance_function=tsp_distance_function,
            enable_incremental=True,
            performance_target_ms=100.0
        )
        
        # 运行演示
        logger.info("开始演示...")
        start_time = time.time()
        
        simulate_evolution_process(
            analyzer=analyzer,
            distance_matrix=distance_matrix,
            num_generations=num_generations,
            population_size=population_size
        )
        
        total_time = time.time() - start_time
        
        # 输出分析器统计信息
        summary = analyzer.get_analysis_summary()
        print(f"\n分析器统计信息:")
        print(f"总分析次数: {summary['total_analyses']}")
        print(f"缓存命中率: {summary['cache_hit_rate']:.2%}")
        print(f"平均分析时间: {summary['performance']['mean_time_ms']:.2f}ms")
        print(f"总运行时间: {total_time:.2f}秒")
        
        logger.info("演示完成")
        
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")
        raise


if __name__ == "__main__":
    main()
