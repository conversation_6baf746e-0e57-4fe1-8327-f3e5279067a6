"""
景观分析器核心模块

实现完整的适应度景观分析功能，包括：
- 景观特征计算
- 增量更新机制
- 智能触发策略
- 性能优化
"""

import time
import logging
from typing import Dict, List, Optional, Callable, Any
import numpy as np

from ..models.data_structures import (
    LandscapeFeatures, PopulationState, AnalysisResult, 
    SearchPhase, Individual, Fitness
)
from ..utils.incremental_stats import IncrementalStatistics, MovingStatistics
from ..utils.sliding_window import SlidingWindowManager
from .feature_calculator import FeatureCalculator
from .history_cache import HistoryCache
from .trigger_manager import TriggerManager


class LandscapeAnalyzer:
    """
    景观分析器主类
    
    协调各个组件完成景观分析任务，提供统一的分析接口。
    """
    
    def __init__(self, 
                 window_size: int = 100,
                 distance_function: Optional[Callable[[Individual, Individual], float]] = None,
                 enable_incremental: bool = True,
                 performance_target_ms: float = 100.0):
        """
        初始化景观分析器
        
        Args:
            window_size: 滑动窗口大小
            distance_function: 个体间距离计算函数
            enable_incremental: 是否启用增量计算
            performance_target_ms: 性能目标（毫秒）
        """
        self.window_size = window_size
        self.distance_function = distance_function or self._default_distance
        self.enable_incremental = enable_incremental
        self.performance_target_ms = performance_target_ms
        
        # 初始化组件
        self.feature_calculator = FeatureCalculator(distance_function=self.distance_function)
        self.sliding_window = SlidingWindowManager(max_size=window_size)
        self.history_cache = HistoryCache(max_size=200)
        self.trigger_manager = TriggerManager()
        
        # 统计信息
        self.analysis_stats = IncrementalStatistics()
        self.performance_stats = MovingStatistics(window_size=20)
        
        # 状态管理
        self.last_analysis_generation = -1
        self.total_analyses = 0
        self.cache_hits = 0
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"景观分析器初始化完成，窗口大小: {window_size}")
    
    def analyze(self, population_state: PopulationState, 
                force_analysis: bool = False) -> Optional[AnalysisResult]:
        """
        执行景观分析
        
        Args:
            population_state: 当前种群状态
            force_analysis: 是否强制执行分析
            
        Returns:
            分析结果，如果不需要分析则返回None
        """
        start_time = time.time()
        
        try:
            # 检查是否需要触发分析
            if not force_analysis and not self._should_analyze(population_state):
                return None
            
            self.logger.info(f"开始景观分析 - 代数: {population_state.generation}")
            
            # 更新滑动窗口
            self._update_sliding_window(population_state)
            
            # 检查缓存
            cached_result = self._check_cache(population_state)
            if cached_result and not force_analysis:
                self.cache_hits += 1
                self.logger.info("使用缓存的分析结果")
                return cached_result
            
            # 执行特征计算
            landscape_features = self._calculate_features(population_state)
            
            # 创建分析结果
            analysis_result = self._create_analysis_result(
                landscape_features, population_state, start_time
            )
            
            # 更新缓存和历史
            self._update_cache_and_history(analysis_result)
            
            # 更新统计信息
            self._update_statistics(analysis_result)
            
            self.total_analyses += 1
            self.last_analysis_generation = population_state.generation
            
            analysis_time = (time.time() - start_time) * 1000
            self.performance_stats.update(analysis_time)
            
            self.logger.info(f"景观分析完成 - 耗时: {analysis_time:.2f}ms")
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"景观分析过程中出错: {e}")
            return None
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """
        获取分析器运行摘要
        
        Returns:
            分析器统计信息
        """
        perf_stats = self.performance_stats.get_statistics()
        
        return {
            "total_analyses": self.total_analyses,
            "cache_hits": self.cache_hits,
            "cache_hit_rate": self.cache_hits / max(1, self.total_analyses),
            "last_analysis_generation": self.last_analysis_generation,
            "performance": {
                "mean_time_ms": perf_stats.get("mean", 0.0),
                "std_time_ms": perf_stats.get("std", 0.0),
                "max_time_ms": perf_stats.get("max", 0.0),
                "target_time_ms": self.performance_target_ms,
                "performance_ratio": perf_stats.get("mean", 0.0) / self.performance_target_ms
            },
            "window_stats": self.sliding_window.get_window_statistics(),
            "cache_stats": self.history_cache.get_cache_statistics()
        }
    
    def update_incremental(self, new_individuals: List[Individual], 
                          new_fitnesses: np.ndarray, generation: int) -> None:
        """
        增量更新景观信息
        
        Args:
            new_individuals: 新个体列表
            new_fitnesses: 新适应度数组
            generation: 当前代数
        """
        if not self.enable_incremental:
            return
        
        try:
            # 更新滑动窗口
            self.sliding_window.add_samples(new_individuals, new_fitnesses, generation)
            
            # 增量更新特征计算器
            self.feature_calculator.update_incremental(new_individuals, new_fitnesses)
            
            self.logger.debug(f"增量更新完成 - 代数: {generation}, 新增样本: {len(new_individuals)}")
            
        except Exception as e:
            self.logger.error(f"增量更新过程中出错: {e}")
    
    def reset(self) -> None:
        """重置分析器状态"""
        self.sliding_window.clear()
        self.history_cache.clear()
        self.trigger_manager.reset()
        self.feature_calculator.reset()
        
        self.analysis_stats.reset()
        self.performance_stats = MovingStatistics(window_size=20)
        
        self.last_analysis_generation = -1
        self.total_analyses = 0
        self.cache_hits = 0
        
        self.logger.info("景观分析器已重置")
    
    def _should_analyze(self, population_state: PopulationState) -> bool:
        """判断是否应该执行分析"""
        # 获取最近的适应度历史
        recent_solutions, recent_fitnesses = self.sliding_window.get_recent_samples(20)
        
        if len(recent_fitnesses) == 0:
            return True  # 首次分析
        
        # 检查触发条件
        trigger_info = self.trigger_manager.check_triggers(
            generation=population_state.generation,
            current_fitness=population_state.best_fitness,
            fitness_history=recent_fitnesses.tolist(),
            diversity=population_state.diversity
        )
        
        return trigger_info["should_trigger"]
    
    def _update_sliding_window(self, population_state: PopulationState) -> None:
        """更新滑动窗口"""
        self.sliding_window.add_samples(
            population_state.individuals,
            population_state.fitnesses,
            population_state.generation
        )
    
    def _check_cache(self, population_state: PopulationState) -> Optional[AnalysisResult]:
        """检查缓存中是否有可用的分析结果"""
        # 简化的缓存检查逻辑
        # 实际应用中可能需要更复杂的相似性判断
        return self.history_cache.get_similar_analysis(
            generation=population_state.generation,
            fitness_signature=self._compute_fitness_signature(population_state.fitnesses)
        )
    
    def _calculate_features(self, population_state: PopulationState) -> LandscapeFeatures:
        """计算景观特征"""
        # 获取计算所需的数据
        recent_solutions, recent_fitnesses = self.sliding_window.get_recent_samples()
        
        if len(recent_solutions) == 0:
            self.logger.warning("没有足够的数据进行景观分析")
            return LandscapeFeatures()
        
        # 计算各项特征
        features = self.feature_calculator.calculate_all_features(
            solutions=recent_solutions,
            fitnesses=recent_fitnesses,
            current_population=population_state
        )
        
        return features
    
    def _create_analysis_result(self, landscape_features: LandscapeFeatures,
                              population_state: PopulationState,
                              start_time: float) -> AnalysisResult:
        """创建分析结果"""
        analysis_time = time.time() - start_time
        
        # 计算置信度分数
        confidence_score = self._calculate_confidence(landscape_features, population_state)
        
        # 获取触发信息
        trigger_info = self.trigger_manager.get_last_trigger_info()
        
        # 计算特征趋势
        feature_trends = self._calculate_feature_trends(landscape_features)
        
        return AnalysisResult(
            landscape_features=landscape_features,
            population_state=population_state,
            trigger_info=trigger_info,
            analysis_time=analysis_time,
            confidence_score=confidence_score,
            feature_trends=feature_trends
        )
    
    def _update_cache_and_history(self, analysis_result: AnalysisResult) -> None:
        """更新缓存和历史记录"""
        self.history_cache.add_analysis(analysis_result)
    
    def _update_statistics(self, analysis_result: AnalysisResult) -> None:
        """更新统计信息"""
        # 更新分析统计
        self.analysis_stats.update(analysis_result.confidence_score)
    
    def _calculate_confidence(self, landscape_features: LandscapeFeatures,
                            population_state: PopulationState) -> float:
        """计算分析结果的置信度"""
        confidence_factors = []
        
        # 基于样本数量的置信度
        sample_confidence = min(1.0, len(population_state.individuals) / 50.0)
        confidence_factors.append(sample_confidence)
        
        # 基于数据质量的置信度
        if population_state.std_fitness > 0:
            quality_confidence = min(1.0, population_state.fitness_range / population_state.std_fitness / 10.0)
        else:
            quality_confidence = 0.5
        confidence_factors.append(quality_confidence)
        
        # 基于历史一致性的置信度
        history_confidence = self._calculate_history_consistency()
        confidence_factors.append(history_confidence)
        
        # 综合置信度
        overall_confidence = np.mean(confidence_factors)
        return max(0.0, min(1.0, overall_confidence))
    
    def _calculate_feature_trends(self, current_features: LandscapeFeatures) -> Dict[str, float]:
        """计算特征趋势"""
        trends = {}
        
        # 获取历史特征
        recent_history = self.history_cache.get_recent_features(window_size=5)
        
        if len(recent_history) < 2:
            return trends
        
        # 计算各特征的趋势
        feature_names = ['ruggedness', 'modality', 'convergence', 'information_content']
        
        for feature_name in feature_names:
            values = [getattr(features, feature_name) for features in recent_history]
            values.append(getattr(current_features, feature_name))
            
            if len(values) >= 3:
                # 计算线性趋势
                x = np.arange(len(values))
                slope, _ = np.polyfit(x, values, 1)
                trends[feature_name] = float(slope)
        
        return trends
    
    def _calculate_history_consistency(self) -> float:
        """计算历史一致性"""
        recent_confidences = self.history_cache.get_recent_confidences(window_size=5)
        
        if len(recent_confidences) < 2:
            return 0.5
        
        # 计算置信度的稳定性
        cv = np.std(recent_confidences) / (np.mean(recent_confidences) + 1e-10)
        consistency = max(0.0, 1.0 - cv)
        
        return consistency
    
    def _compute_fitness_signature(self, fitnesses: np.ndarray) -> str:
        """计算适应度签名用于缓存"""
        if len(fitnesses) == 0:
            return "empty"
        
        # 简化的签名计算
        mean_fitness = np.mean(fitnesses)
        std_fitness = np.std(fitnesses)
        
        return f"{mean_fitness:.3f}_{std_fitness:.3f}_{len(fitnesses)}"
    
    def _default_distance(self, individual1: Individual, individual2: Individual) -> float:
        """默认的个体距离计算函数"""
        # 这里需要根据具体的个体表示来实现
        # 对于TSP问题，可以计算路径的编辑距离
        # 这里提供一个简化的实现
        try:
            if hasattr(individual1, '__len__') and hasattr(individual2, '__len__'):
                if len(individual1) != len(individual2):
                    return float('inf')
                
                # 计算汉明距离的归一化版本
                differences = sum(1 for a, b in zip(individual1, individual2) if a != b)
                return differences / len(individual1)
            else:
                # 如果不是序列类型，尝试直接比较
                return 0.0 if individual1 == individual2 else 1.0
        except Exception:
            return 1.0  # 默认距离
