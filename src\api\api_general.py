import os
import json
import time
import logging
import requests
from config.config import API_CONFIG

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class InterfaceAPI:
    """通用API接口类，支持多种LLM模型"""
    
    def __init__(self, api_type="gemini", debug_mode=False):
        """初始化API接口
        
        参数:
            api_type (str): API类型，支持 'gemini', 'deepseek', 'xfyun', 'zhipu'
            debug_mode (bool): 是否开启调试模式
        """
        self.api_type = api_type
        self.config = API_CONFIG.get(api_type, {})
        self.logger = logging.getLogger(f"InterfaceAPI.{api_type}")
        self.debug_mode = debug_mode
        self.logger.info(f"初始化 {api_type} API接口")
        
        # 初始化特定API
        if api_type == "gemini":
            self._init_gemini()
        elif api_type == "deepseek":
            self._init_deepseek()
        elif api_type == "xfyun":
            self._init_xfyun()
        elif api_type == "zhipu":
            self._init_zhipu()
        else:
            self.logger.error(f"不支持的API类型: {api_type}")
            raise ValueError(f"不支持的API类型: {api_type}")
    
    def _init_gemini(self):
        """初始化Google Gemini API"""
        # Gemini API使用requests手动调用，不需要特殊初始化
        self.logger.info(f"Gemini API配置完成，模型: {self.config.get('model')}")
    
    def _init_deepseek(self):
        """初始化DeepSeek API"""
        # DeepSeek API不需要特殊初始化，只需要保存配置
        self.logger.info(f"DeepSeek API配置完成，模型: {self.config.get('model')}")
    
    def _init_xfyun(self):
        """初始化讯飞星火API"""
        # 讯飞星火API不需要特殊初始化，只需要保存配置
        self.logger.info(f"讯飞星火API配置完成，模型: {self.config.get('model')}")
    
    def _init_zhipu(self):
        """初始化智谱API"""
        # 智谱API不需要特殊初始化，只需要保存配置
        self.logger.info(f"智谱API配置完成，模型: {self.config.get('model')}")

    def _make_request(self, url, headers, data, max_retries, retry_delay, api_name, response_parser):
        """
        通用请求方法

        参数:
            url (str): 请求URL
            headers (dict): 请求头
            data (dict): 请求数据
            max_retries (int): 最大重试次数
            retry_delay (int): 重试延迟
            api_name (str): API名称
            response_parser (function): 用于解析响应的回调函数
        
        返回:
            str: API响应文本
        """
        retries = 0
        while retries < max_retries:
            try:
                self.logger.info(f"发送请求到{api_name} API (尝试 {retries+1}/{max_retries})")
                response = requests.post(url, headers=headers, json=data, timeout=10)
                response.raise_for_status()
                response_data = response.json()
                
                # 使用回调函数解析响应
                return response_parser(response_data)

            except requests.exceptions.RequestException as e:
                self.logger.error(f"{api_name} API请求网络错误: {e}")
                retries += 1
                if retries >= max_retries:
                    return f"API请求失败: {e}"
                if self.debug_mode:
                    self.logger.debug(f"重试中... ({retries}/{max_retries})")
                time.sleep(retry_delay)
            except json.JSONDecodeError as e:
                self.logger.error(f"{api_name} API响应解析错误: {e}")
                return f"API响应解析失败: {e}"
            except Exception as e:
                self.logger.error(f"{api_name} API请求未知错误: {e}")
                retries += 1
                if retries >= max_retries:
                    return f"API请求失败: {e}"
                if self.debug_mode:
                    self.logger.debug(f"重试中... ({retries}/{max_retries})")
                time.sleep(retry_delay)

        self.logger.error("达到最大重试次数，退出。")
        return "API请求失败: 达到最大重试次数"

    def _parse_gemini_response(self, response_data):
        if "candidates" in response_data and response_data["candidates"]:
            candidate = response_data["candidates"][0]
            if "content" in candidate and "parts" in candidate["content"]:
                return candidate["content"]["parts"][0]["text"]
        
        self.logger.error(f"API 响应格式不符合预期: {response_data}")
        return f"API 响应格式不符合预期: {response_data}"

    def _parse_standard_chat_response(self, response_data):
        if "error" in response_data:
            error_msg = response_data.get("error", {}).get("message", "未知错误")
            self.logger.error(f"API返回错误: {error_msg}")
            return f"API请求失败: {error_msg}"
        
        if "choices" in response_data and len(response_data["choices"]) > 0:
            return response_data["choices"][0]["message"]["content"]
        
        self.logger.error(f"API响应格式异常: {response_data}")
        return "API响应格式异常"

    def get_gemini_response(self, prompt_content, max_retries=3, retry_delay=20):
        """
        调用 Google Gemini API 获取响应
        """
        api_key = self.config.get("api_key")
        model = self.config.get("model")
        api_base = self.config.get("api_base")

        if not all([api_key, model, api_base]):
            self.logger.error("Gemini API配置不完整")
            return "Gemini API配置不完整"

        url = f"{api_base}/{model}:generateContent?key={api_key}"
        
        headers = { "Content-Type": "application/json" }
        
        data = {
            "contents": [{"parts": [{"text": prompt_content}]}]
        }
        
        return self._make_request(url, headers, data, max_retries, retry_delay, "Gemini", self._parse_gemini_response)

    def get_deepseek_response(self, prompt, max_retries=3, retry_delay=2):
        """获取DeepSeek API响应"""
        if self.api_type != "deepseek":
            self.logger.warning(f"当前API类型为 {self.api_type}，但调用了get_deepseek_response")
        
        api_base = self.config.get("api_base")
        api_key = self.config.get("api_key")
        model = self.config.get("model")

        if not all([api_base, api_key, model]):
            self.logger.error("DeepSeek API配置不完整")
            return "DeepSeek API配置不完整"
        
        url = f"{api_base}/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2048
        }
        
        return self._make_request(url, headers, data, max_retries, retry_delay, "DeepSeek", self._parse_standard_chat_response)

    def get_xfyun_response(self, prompt, max_retries=3, retry_delay=2):
        """获取讯飞星火API响应"""
        if self.api_type != "xfyun":
            self.logger.warning(f"当前API类型为 {self.api_type}，但调用了get_xfyun_response")
        
        api_endpoint = self.config.get("endpoint")
        api_key = self.config.get("api_key")
        model = self.config.get("model")

        if not all([api_endpoint, api_key, model]):
            self.logger.error("讯飞星火API配置不完整")
            return "讯飞星火API配置不完整"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2048
        }
        
        return self._make_request(api_endpoint, headers, data, max_retries, retry_delay, "讯飞星火", self._parse_standard_chat_response)

    def _get_zhipu_token(self):
        """生成智谱API的认证token"""
        api_key = self.config.get("api_key")
        if not api_key:
            self.logger.error("智谱API密钥未配置")
            return None, "API密钥未配置"

        try:
            import jwt
            import datetime
            
            key_parts = api_key.split('.')
            if len(key_parts) != 2:
                self.logger.error("智谱API密钥格式错误")
                return None, "API密钥格式错误"
            
            api_id, api_secret = key_parts
            
            exp_time = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1)
            payload = {
                "api_key": api_id,
                "exp": int(exp_time.timestamp()),
                "timestamp": int(datetime.datetime.now(datetime.timezone.utc).timestamp())
            }
            return jwt.encode(payload, api_secret, algorithm="HS256"), None
            
        except ImportError:
            msg = "缺少PyJWT库，请安装 `pip install PyJWT`"
            self.logger.error(msg)
            return None, msg
        except Exception as e:
            msg = f"生成智谱API认证token失败: {e}"
            self.logger.error(msg)
            return None, msg

    def get_zhipu_response(self, prompt, max_retries=3, retry_delay=2):
        """获取智谱API响应"""
        if self.api_type != "zhipu":
            self.logger.warning(f"当前API类型为 {self.api_type}，但调用了get_zhipu_response")

        token, error = self._get_zhipu_token()
        if error:
            return error

        model = self.config.get("model")
        if not model:
            self.logger.error("智谱API模型未配置")
            return "智谱API模型未配置"
        
        api_endpoint = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2048
        }
        
        return self._make_request(api_endpoint, headers, data, max_retries, retry_delay, "智谱", self._parse_standard_chat_response)
    
    def get_response(self, prompt, max_retries=3, retry_delay=2):
        """根据当前API类型获取响应
        
        参数:
            prompt (str): 提示词
            max_retries (int): 最大重试次数
            retry_delay (int): 重试延迟时间(秒)
            
        返回:
            str: API响应文本
        """
        if self.api_type == "gemini":
            return self.get_gemini_response(prompt, max_retries, retry_delay)
        elif self.api_type == "deepseek":
            return self.get_deepseek_response(prompt, max_retries, retry_delay)
        elif self.api_type == "xfyun":
            return self.get_xfyun_response(prompt, max_retries, retry_delay)
        elif self.api_type == "zhipu":
            return self.get_zhipu_response(prompt, max_retries, retry_delay)
        else:
            self.logger.error(f"不支持的API类型: {self.api_type}")
            return f"不支持的API类型: {self.api_type}"