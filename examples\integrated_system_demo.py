"""
景观指导进化算法系统集成演示

展示景观分析与LLM策略选择的完整集成使用。
"""

import sys
import os
import asyncio
import numpy as np
import logging
from typing import List, Any
import time

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.landscape_analysis import (
    LandscapeAnalyzer, 
    PopulationState
)
from src.llm_strategy import (
    StrategySelector,
    LLMConfig,
    StrategyExecutionResult
)


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('integrated_system_demo.log')
        ]
    )


def generate_tsp_individual(size: int) -> List[int]:
    """生成TSP个体（随机排列）"""
    individual = list(range(size))
    np.random.shuffle(individual)
    return individual


def calculate_tsp_fitness(individual: List[int], distance_matrix: np.ndarray) -> float:
    """计算TSP适应度（路径长度的倒数）"""
    total_distance = 0.0
    n = len(individual)
    
    for i in range(n):
        from_city = individual[i]
        to_city = individual[(i + 1) % n]
        total_distance += distance_matrix[from_city][to_city]
    
    return 1.0 / (1.0 + total_distance)


def generate_distance_matrix(size: int) -> np.ndarray:
    """生成随机距离矩阵"""
    np.random.seed(42)
    matrix = np.random.rand(size, size) * 100
    matrix = (matrix + matrix.T) / 2
    np.fill_diagonal(matrix, 0)
    return matrix


def tsp_distance_function(individual1: List[int], individual2: List[int]) -> float:
    """计算两个TSP个体之间的距离"""
    if len(individual1) != len(individual2):
        return 1.0
    
    differences = sum(1 for a, b in zip(individual1, individual2) if a != b)
    return differences / len(individual1)


def calculate_population_diversity(population: List[List[int]]) -> float:
    """计算种群多样性"""
    if len(population) < 2:
        return 0.0
    
    total_distance = 0.0
    pair_count = 0
    
    for i in range(len(population)):
        for j in range(i + 1, len(population)):
            distance = tsp_distance_function(population[i], population[j])
            total_distance += distance
            pair_count += 1
    
    return total_distance / pair_count if pair_count > 0 else 0.0


def apply_strategy_decision(population: List[List[int]], 
                          fitnesses: np.ndarray,
                          strategy_decision,
                          distance_matrix: np.ndarray) -> List[List[int]]:
    """应用策略决策到种群"""
    params = strategy_decision.strategy_parameters
    new_population = []
    population_size = len(population)
    
    # 精英保留
    elite_count = max(1, int(population_size * params.elite_preservation_rate))
    elite_indices = np.argsort(fitnesses)[-elite_count:]
    for idx in elite_indices:
        new_population.append(population[idx].copy())
    
    # 生成剩余个体
    while len(new_population) < population_size:
        # 选择父代
        parent1_idx = roulette_wheel_selection(fitnesses, params.selection_pressure)
        parent2_idx = roulette_wheel_selection(fitnesses, params.selection_pressure)
        
        # 交叉
        if np.random.random() < params.crossover_rate:
            child = order_crossover(population[parent1_idx], population[parent2_idx])
        else:
            child = population[parent1_idx].copy()
        
        # 变异
        if np.random.random() < params.mutation_rate:
            child = swap_mutation(child)
        
        # 局部搜索
        if np.random.random() < params.local_search_intensity:
            child = local_search(child, distance_matrix, max_iterations=5)
        
        new_population.append(child)
    
    return new_population[:population_size]


def roulette_wheel_selection(fitnesses: np.ndarray, pressure: float = 2.0) -> int:
    """轮盘赌选择"""
    # 应用选择压力
    adjusted_fitnesses = np.power(fitnesses, pressure)
    total_fitness = np.sum(adjusted_fitnesses)
    
    if total_fitness <= 0:
        return np.random.randint(len(fitnesses))
    
    selection_point = np.random.random() * total_fitness
    cumulative_fitness = 0.0
    
    for i, fitness in enumerate(adjusted_fitnesses):
        cumulative_fitness += fitness
        if cumulative_fitness >= selection_point:
            return i
    
    return len(fitnesses) - 1


def order_crossover(parent1: List[int], parent2: List[int]) -> List[int]:
    """顺序交叉"""
    size = len(parent1)
    start = np.random.randint(0, size)
    end = np.random.randint(start + 1, size + 1)
    
    child = [-1] * size
    child[start:end] = parent1[start:end]
    
    pointer = end % size
    for city in parent2[end:] + parent2[:end]:
        if city not in child:
            child[pointer] = city
            pointer = (pointer + 1) % size
    
    return child


def swap_mutation(individual: List[int]) -> List[int]:
    """交换变异"""
    mutated = individual.copy()
    size = len(mutated)
    
    if size > 1:
        i, j = np.random.choice(size, 2, replace=False)
        mutated[i], mutated[j] = mutated[j], mutated[i]
    
    return mutated


def local_search(individual: List[int], distance_matrix: np.ndarray, 
                max_iterations: int = 10) -> List[int]:
    """简单的2-opt局部搜索"""
    current = individual.copy()
    current_fitness = calculate_tsp_fitness(current, distance_matrix)
    
    for _ in range(max_iterations):
        improved = False
        size = len(current)
        
        for i in range(size - 1):
            for j in range(i + 2, size):
                # 2-opt交换
                new_individual = current.copy()
                new_individual[i+1:j+1] = reversed(new_individual[i+1:j+1])
                
                new_fitness = calculate_tsp_fitness(new_individual, distance_matrix)
                
                if new_fitness > current_fitness:
                    current = new_individual
                    current_fitness = new_fitness
                    improved = True
                    break
            
            if improved:
                break
        
        if not improved:
            break
    
    return current


async def run_integrated_evolution(analyzer: LandscapeAnalyzer,
                                 strategy_selector: StrategySelector,
                                 distance_matrix: np.ndarray,
                                 num_generations: int = 30,
                                 population_size: int = 50) -> None:
    """运行集成的进化过程"""
    logger = logging.getLogger(__name__)
    problem_size = distance_matrix.shape[0]
    
    logger.info(f"开始集成进化过程 - 问题规模: {problem_size}, 种群大小: {population_size}, 代数: {num_generations}")
    
    # 初始化种群
    population = [generate_tsp_individual(problem_size) for _ in range(population_size)]
    
    best_fitness_history = []
    mean_fitness_history = []
    strategy_history = []
    
    for generation in range(num_generations):
        # 计算适应度
        fitnesses = np.array([
            calculate_tsp_fitness(individual, distance_matrix) 
            for individual in population
        ])
        
        # 计算多样性
        diversity = calculate_population_diversity(population)
        
        # 记录统计信息
        best_fitness = np.max(fitnesses)
        mean_fitness = np.mean(fitnesses)
        best_fitness_history.append(best_fitness)
        mean_fitness_history.append(mean_fitness)
        
        # 创建种群状态
        population_state = PopulationState(
            individuals=population.copy(),
            fitnesses=fitnesses,
            generation=generation,
            diversity=diversity
        )
        
        # 执行景观分析
        analysis_result = analyzer.analyze(population_state)
        
        if analysis_result:
            # 使用LLM选择策略
            strategy_decision = await strategy_selector.select_strategy(analysis_result)
            strategy_history.append(strategy_decision)
            
            # 应用策略决策
            old_best_fitness = best_fitness
            population = apply_strategy_decision(
                population, fitnesses, strategy_decision, distance_matrix
            )
            
            # 计算新的适应度以评估策略效果
            new_fitnesses = np.array([
                calculate_tsp_fitness(individual, distance_matrix) 
                for individual in population
            ])
            new_best_fitness = np.max(new_fitnesses)
            
            # 计算策略效果
            fitness_improvement = new_best_fitness - old_best_fitness
            
            # 创建执行结果
            execution_result = StrategyExecutionResult(
                strategy_decision=strategy_decision,
                execution_success=True,
                fitness_improvement=fitness_improvement,
                strategy_effectiveness=min(1.0, max(0.0, fitness_improvement * 10))
            )
            
            # 记录执行结果
            strategy_selector.record_execution_result(execution_result)
            
            # 输出进度信息
            features = analysis_result.landscape_features
            logger.info(f"代数 {generation}: 最佳适应度={best_fitness:.6f}, "
                       f"平均适应度={mean_fitness:.6f}, 多样性={diversity:.3f}")
            logger.info(f"  景观特征: 崎岖度={features.ruggedness:.3f}, "
                       f"多模态性={features.modality:.3f}, 收敛度={features.convergence:.3f}")
            logger.info(f"  策略: {strategy_decision.global_strategy.value}, "
                       f"置信度={strategy_decision.confidence:.3f}, "
                       f"改善={fitness_improvement:.6f}")
        
        else:
            logger.warning(f"代数 {generation}: 景观分析失败，使用默认策略")
            # 使用简单的默认进化策略
            population = apply_default_evolution(population, fitnesses, distance_matrix)
    
    # 输出最终统计
    print_final_statistics(
        best_fitness_history, mean_fitness_history, 
        strategy_history, analyzer, strategy_selector
    )


def apply_default_evolution(population: List[List[int]], 
                          fitnesses: np.ndarray,
                          distance_matrix: np.ndarray) -> List[List[int]]:
    """应用默认进化策略"""
    new_population = []
    population_size = len(population)
    
    # 精英保留
    elite_count = max(1, population_size // 10)
    elite_indices = np.argsort(fitnesses)[-elite_count:]
    for idx in elite_indices:
        new_population.append(population[idx].copy())
    
    # 生成剩余个体
    while len(new_population) < population_size:
        parent1_idx = roulette_wheel_selection(fitnesses)
        parent2_idx = roulette_wheel_selection(fitnesses)
        
        child = order_crossover(population[parent1_idx], population[parent2_idx])
        
        if np.random.random() < 0.1:
            child = swap_mutation(child)
        
        new_population.append(child)
    
    return new_population[:population_size]


def print_final_statistics(best_fitness_history: List[float],
                         mean_fitness_history: List[float],
                         strategy_history: List[Any],
                         analyzer: LandscapeAnalyzer,
                         strategy_selector: StrategySelector) -> None:
    """打印最终统计信息"""
    print("\n" + "="*80)
    print("集成系统演示结果")
    print("="*80)
    
    # 进化统计
    print(f"最终最佳适应度: {best_fitness_history[-1]:.6f}")
    print(f"初始最佳适应度: {best_fitness_history[0]:.6f}")
    print(f"总体改善: {best_fitness_history[-1] - best_fitness_history[0]:.6f}")
    print(f"改善率: {((best_fitness_history[-1] / best_fitness_history[0]) - 1) * 100:.2f}%")
    
    # 景观分析统计
    analyzer_stats = analyzer.get_analysis_summary()
    print(f"\n景观分析统计:")
    print(f"总分析次数: {analyzer_stats['total_analyses']}")
    print(f"缓存命中率: {analyzer_stats['cache_hit_rate']:.2%}")
    print(f"平均分析时间: {analyzer_stats['performance']['mean_time_ms']:.2f}ms")
    
    # 策略选择统计
    selector_stats = strategy_selector.get_statistics()
    print(f"\n策略选择统计:")
    print(f"总决策次数: {selector_stats['total_decisions']}")
    print(f"成功率: {selector_stats['success_rate']:.2%}")
    print(f"缓存命中率: {selector_stats['cache_hit_rate']:.2%}")
    print(f"平均决策时间: {selector_stats['avg_decision_time']:.2f}秒")
    
    # 策略使用统计
    if strategy_history:
        strategy_types = [s.global_strategy.value for s in strategy_history]
        strategy_counts = {}
        for strategy in strategy_types:
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        print(f"\n策略使用分布:")
        for strategy, count in strategy_counts.items():
            percentage = (count / len(strategy_history)) * 100
            print(f"  {strategy}: {count}次 ({percentage:.1f}%)")
        
        # 平均置信度
        avg_confidence = np.mean([s.confidence for s in strategy_history])
        print(f"平均策略置信度: {avg_confidence:.3f}")


async def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("景观指导进化算法系统集成演示")
    print("="*60)
    
    # 配置参数
    problem_size = 15  # TSP城市数量
    population_size = 40
    num_generations = 30
    
    try:
        # 生成TSP问题实例
        logger.info("生成TSP问题实例...")
        distance_matrix = generate_distance_matrix(problem_size)
        
        # 初始化景观分析器
        logger.info("初始化景观分析器...")
        analyzer = LandscapeAnalyzer(
            window_size=100,
            distance_function=tsp_distance_function,
            enable_incremental=True,
            performance_target_ms=100.0
        )
        
        # 初始化策略选择器
        logger.info("初始化策略选择器...")
        llm_config = LLMConfig(
            model_name="gpt-4",
            max_tokens=2000,
            temperature=0.7
        )
        
        strategy_selector = StrategySelector(
            llm_config=llm_config,
            use_mock_llm=True,  # 使用模拟LLM进行演示
            enable_caching=True
        )
        
        # 运行集成演示
        logger.info("开始集成演示...")
        start_time = time.time()
        
        await run_integrated_evolution(
            analyzer=analyzer,
            strategy_selector=strategy_selector,
            distance_matrix=distance_matrix,
            num_generations=num_generations,
            population_size=population_size
        )
        
        total_time = time.time() - start_time
        print(f"\n总运行时间: {total_time:.2f}秒")
        
        logger.info("集成演示完成")
        
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
